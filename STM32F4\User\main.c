/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   STM32F4竞赛级模块库集成示例 - 嘉立创天空星STM32F407VGT6
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>

// 竞赛级模块库 - 硬件适配修正版
#include "../Modules/Core/systick.h"
#include "../Modules/Core/usart.h"
#include "../Modules/Acquisition/adc_dma.h"
#include "../Modules/Acquisition/parallel_adc.h"
#include "../Modules/Generation/dds_wavegen.h"
#include "../Modules/Interface/oled.h"          // I2C OLED显示模块(修正版)
#include "../Modules/Interface/key.h"           // 按键扫描模块(简化版)

// 电赛G题专用驱动模块 - 基于商家代码优化
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动(优化版)
#include "../Modules/Acquisition/ad7606.h"     // AD7606同步ADC驱动(优化版)
#include "../Modules/Interface/cd4052.h"       // CD4052增益控制驱动(优化版)

// #include "fft.h"           // FFT模块(需要CMSIS-DSP库配置)
// #include "input_capture.h" // 频率测量模块
// #include "pwm.h"           // PWM生成模块


/* Private variables ---------------------------------------------------------*/
static char debug_buffer[256];

// 电赛G题专用数据缓冲区
static int16_t g_adc7606_data[AD7606_CHANNEL_COUNT];
static uint8_t g_current_gain_level = CD4052_GAIN_LEVEL_0;
static float g_dac_voltage_a = 2.5f;
static float g_dac_voltage_b = 1.0f;

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Module_Init_Demo(void);
void Module_Test_Demo(void);

// 电赛G题专用函数
uint8_t G_Module_Init(void);
void G_Module_Test(void);
void G_Comprehensive_Test(void);

/**
  * @brief  Main program - 竞赛级模块库集成示例
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统时钟初始化 */
    SystemInit();

    /* 初始化竞赛级模块库 */
    Module_Init_Demo();

    /* 初始化电赛G题专用模块 */
    if (G_Module_Init() != 0) {
        USART_Printf("G题模块初始化失败，程序停止\r\n");
        while (1);
    }

    /* 模块功能测试演示 */
    Module_Test_Demo();

    /* G题专用模块测试 */
    G_Module_Test();

    /* 主循环 - 简化版本，专注于模块集成演示 */
    while (1)
    {
        /* 检查ADC数据是否就绪 */
        if (g_adc_dma_full_complete) {
            g_adc_dma_full_complete = false;

            // 处理ADC数据
            ADC_ProcessData();

            // 通过串口输出统计信息
            ADC_Stats_t adc_stats;
            ADC_GetStats(&adc_stats);

            sprintf(debug_buffer, "ADC: Samples=%lu, Rate=%.1fkSPS\r\n",
                    adc_stats.total_samples, adc_stats.actual_sample_rate/1000.0f);
            USART_SendString(debug_buffer);
        }

        /* 检查AD7606数据采集 */
        static uint32_t last_ad7606_time = 0;
        if (SysTick_GetTick() - last_ad7606_time > 100) { // 每100ms采集一次
            last_ad7606_time = SysTick_GetTick();

            uint8_t result = AD7606_ReadData(g_adc7606_data);
            if (result == AD7606_OK) {
                sprintf(debug_buffer, "AD7606: CH0=%d, CH1=%d, CH2=%d, CH3=%d\r\n",
                        g_adc7606_data[0], g_adc7606_data[1], g_adc7606_data[2], g_adc7606_data[3]);
                USART_SendString(debug_buffer);
            }
        }

        /* 检查并行ADC数据 */
        if (g_parallel_adc_data_ready) {
            g_parallel_adc_data_ready = false;

            uint16_t data;
            if (ParallelADC_ReadSingle(&data) == 0) {
                sprintf(debug_buffer, "Parallel ADC: %d\r\n", data);
                USART_SendString(debug_buffer);
            }
        }

        /* 检查DDS更新 */
        if (g_dds_update_complete) {
            g_dds_update_complete = false;

            DDS_Stats_t dds_stats;
            DDS_GetStats(&dds_stats);

            sprintf(debug_buffer, "DDS: Freq=%.1fHz, Samples=%lu\r\n",
                    dds_stats.actual_frequency, dds_stats.output_samples);
            USART_SendString(debug_buffer);
        }

        /* 检查按键 */
        Key_Number_t key = Key_Scan();
        if (key != KEY_NONE) {
            sprintf(debug_buffer, "按键按下: KEY_%d\r\n", key - 1);
            USART_SendString(debug_buffer);

            // 按键功能演示 - 集成G题功能
            if (key == KEY_0) {
                // KEY0: 切换增益级别
                g_current_gain_level = (g_current_gain_level + 1) % 4;
                uint8_t result = CD4052_SetGain(g_current_gain_level);

                if (result == CD4052_OK) {
                    sprintf(debug_buffer, "Gain Level Change: %d\r\n", g_current_gain_level);
                    USART_SendString(debug_buffer);

                    // 更新OLED显示
                    OLED_Clear();
                    OLED_ShowString(0, 0, "Gain Changed", OLED_FONT_6x8);
                    sprintf(debug_buffer, "Level: %d", g_current_gain_level);
                    OLED_ShowString(0, 16, (const char*)debug_buffer, OLED_FONT_6x8);
                    OLED_Refresh();
                }
            }

            if (key == KEY_1) {
                // KEY1: 调整DAC输出电压
                g_dac_voltage_a += 0.5f;
                if (g_dac_voltage_a > 5.0f) g_dac_voltage_a = 0.0f;

                uint8_t result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, g_dac_voltage_a);
                if (result == DAC8552_OK) {
                    sprintf(debug_buffer, "DAC_A Voltage: %.1fV\r\n", g_dac_voltage_a);
                    USART_SendString(debug_buffer);

                    // 更新OLED显示
                    OLED_Clear();
                    OLED_ShowString(0, 0, "DAC_A Changed", OLED_FONT_6x8);
                    sprintf(debug_buffer, "%.1fV", g_dac_voltage_a);
                    OLED_ShowString(0, 16, (const char*)debug_buffer, OLED_FONT_6x8);
                    OLED_Refresh();
                }
            }
        }

        /* 系统状态监控 */
        static uint32_t last_status_time = 0;
        if (SysTick_GetTick() - last_status_time > 5000) { // 每5秒输出一次状态
            last_status_time = SysTick_GetTick();

            sprintf(debug_buffer, "System Uptime: %lu ms\r\n", SysTick_GetUptime_ms());
            USART_SendString(debug_buffer);
        }

        /* 短暂延时，降低CPU占用 */
        Delay_ms(10);
    }
}

/**
  * @brief  竞赛级模块库初始化演示
  * @param  None
  * @retval None
  */
void Module_Init_Demo(void)
{
    /* 1. 初始化SysTick高精度延时模块 */
    if (SysTick_Init() != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 2. 初始化USART调试模块 */
    if (USART1_Init(115200) != 0) {
        // 初始化失败处理
        while (1);
    }

    /* 输出启动信息 */
    USART_Printf("\r\n=== STM32F4竞赛级模块库演示 ===\r\n");
    USART_Printf("系统时钟: %lu MHz\r\n", SystemCoreClock / 1000000);
    USART_Printf("编译时间: %s %s\r\n", __DATE__, __TIME__);

    /* 3. 初始化ADC+DMA多通道采集模块 - 简化版 */
    ADC_Config_t adc_config = {
        .sample_rate = 100000,          // 100kSPS (保守设置)
        .resolution = ADC_RESOLUTION_12BIT,
        .mode = ADC_MODE_CONTINUOUS     // 连续采样模式
    };

    if (ADC1_DMA_Init(&adc_config) == 0) {
        USART_Printf("ADC+DMA模块初始化成功\r\n");
        ADC_Start_Acquisition();
    } else {
        USART_Printf("ADC+DMA模块初始化失败\r\n");
    }

    /* 4. 初始化14位并行ADC接口模块 - LTC2246 */
    ParallelADC_Config_t parallel_config = {
        .max_sample_rate = 1000000,     // 1MSPS (保守设置)
        .data_width = 14,               // 14位数据 (LTC2246)
        .enable_data_valid = false,     // 禁用数据有效信号
        .enable_overflow_detect = false, // 禁用溢出检测
        .trigger_edge = PARALLEL_ADC_TRIGGER_RISING // 上升沿触发
    };

    if (ParallelADC_Init(&parallel_config) == 0) {
        USART_Printf("并行ADC模块初始化成功\r\n");
        ParallelADC_Start();
    } else {
        USART_Printf("并行ADC模块初始化失败\r\n");
    }

    /* 5. 初始化DDS波形生成模块 - 简化版 */
    DDS_Config_t dds_config = {
        .frequency = 1000,              // 1kHz
        .amplitude = 2048,              // 50%幅度
        .offset = 2048,                 // 中心偏移
        .wave_type = DDS_WAVE_SINE,     // 正弦波
        .sample_rate = 1000000          // 1MSPS
    };

    if (DDS_Init(&dds_config) == 0) {
        USART_Printf("DDS波形生成模块初始化成功\r\n");
        DDS_Start();
    } else {
        USART_Printf("DDS波形生成模块初始化失败\r\n");
    }

    /* 6. 初始化I2C OLED显示模块 */
    if (OLED_Init() == 0) {
        USART_Printf("I2C OLED显示模块初始化成功\r\n");
        OLED_Clear();
        OLED_ShowString(0, 0, "STM32F407", OLED_FONT_6x8);
        OLED_ShowString(0, 16, "Contest Lib", OLED_FONT_6x8);
        OLED_ShowString(0, 32, "Hardware Fix", OLED_FONT_6x8);
        OLED_ShowString(0, 48, "Ready!", OLED_FONT_6x8);
        OLED_Refresh();
    } else {
        USART_Printf("I2C OLED显示模块初始化失败\r\n");
    }

    /* 7. 初始化按键扫描模块 */
    if (Key_Init() == 0) {
        USART_Printf("按键扫描模块初始化成功\r\n");
    } else {
        USART_Printf("按键扫描模块初始化失败\r\n");
    }

    USART_Printf("所有模块初始化完成！\r\n\r\n");
}

/**
  * @brief  模块功能测试演示
  * @param  None
  * @retval None
  */
void Module_Test_Demo(void)
{
    USART_Printf("Start Module Test...\r\n");

    /* 测试SysTick精确延时 */
    uint32_t start_time = SysTick_GetTick();
    Delay_ms(100);
    uint32_t elapsed = SysTick_GetTick() - start_time;
    USART_Printf("SysTick Delay Test: Expected 100ms, Actual %lums\r\n", elapsed);

    /* 测试DDS频率设置 */
    DDS_SetFrequency(2000); // 设置为2kHz
    USART_Printf("DDS Frequency Set to 2kHz\r\n");

    Delay_ms(1000);

    DDS_SetWaveType(DDS_WAVE_SQUARE); // 切换为方波
    USART_Printf("DDS波形切换为方波\r\n");

    /* 测试ADC校准 */
    if (ADC_Calibrate() == 0) {
        USART_Printf("ADC校准完成\r\n");
    }

    USART_Printf("模块功能测试完成！\r\n\r\n");
}

/**
  * @brief  电赛G题专用模块初始化
  * @param  None
  * @retval 0: 成功, 1: 失败
  */
uint8_t G_Module_Init(void)
{
    uint8_t result;

    USART_Printf("=== 电赛G题专用模块初始化 ===\r\n");

    /* 1. 初始化BSP */
    BSP_Init();
    USART_Printf("BSP初始化完成\r\n");

    /* 2. 初始化DAC8552 */
    result = DAC8552_Init();
    if (result != DAC8552_OK) {
        USART_Printf("DAC8552 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("DAC8552初始化成功\r\n");

    /* 3. 初始化CD4052 */
    result = CD4052_Init();
    if (result != CD4052_OK) {
        USART_Printf("CD4052 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("CD4052初始化成功\r\n");

    /* 4. 初始化AD7606 */
    result = AD7606_Init();
    if (result != AD7606_OK) {
        USART_Printf("AD7606 Init Failed: %d\r\n", result);
        return 1;
    }
    USART_Printf("AD7606初始化成功\r\n");

    USART_Printf("G题专用模块初始化完成！\r\n\r\n");
    return 0;
}

/**
  * @brief  G题专用模块测试
  * @param  None
  * @retval None
  */
void G_Module_Test(void)
{
    uint8_t result;

    USART_Printf("=== G Module Test ===\r\n");

    /* 1. DAC输出测试 */
    USART_Printf("DAC8552输出测试...\r\n");
    result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, 2.5f);
    if (result == DAC8552_OK) {
        USART_Printf("DAC通道A输出2.5V - 成功\r\n");
    }

    result = DAC8552_SetVoltage(DAC8552_CHANNEL_B, 1.0f);
    if (result == DAC8552_OK) {
        USART_Printf("DAC通道B输出1.0V - 成功\r\n");
    }

    /* 2. 增益控制测试 */
    USART_Printf("CD4052增益控制测试...\r\n");
    for (uint8_t level = 0; level < 4; level++) {
        result = CD4052_SetGain(level);
        if (result == CD4052_OK) {
            USART_Printf("增益级别%d设置成功\r\n", level);
        }
        Delay_ms(100);
    }

    /* 3. ADC采集测试 */
    USART_Printf("AD7606采集测试...\r\n");
    result = AD7606_ReadData(g_adc7606_data);
    if (result == AD7606_OK) {
        USART_Printf("AD7606采集成功: ");
        for (uint8_t i = 0; i < 4; i++) {
            USART_Printf("CH%d=%d ", i, g_adc7606_data[i]);
        }
        USART_Printf("\r\n");
    }

    USART_Printf("G题专用模块测试完成！\r\n\r\n");
}

/**
  * @brief  G题综合测试 - 模拟实际应用
  * @param  None
  * @retval None
  */
void G_Comprehensive_Test(void)
{
    uint8_t result;

    USART_Printf("=== G Comprehensive Test ===\r\n");

    for (uint8_t cycle = 0; cycle < 3; cycle++) {
        USART_Printf("--- 测试周期 %d ---\r\n", cycle + 1);

        /* 设置DAC输出 */
        float voltage = 1.0f + cycle * 1.0f;
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, voltage);
        if (result == DAC8552_OK) {
            USART_Printf("DAC输出: %.1fV\r\n", voltage);
        }

        /* 设置增益级别 */
        result = CD4052_SetGain(cycle % 4);
        if (result == CD4052_OK) {
            USART_Printf("增益级别: %d\r\n", cycle % 4);
        }

        /* 等待系统稳定 */
        Delay_ms(50);

        /* ADC采集 */
        result = AD7606_ReadData(g_adc7606_data);
        if (result == AD7606_OK) {
            USART_Printf("ADC结果: CH0=%d, CH1=%d\r\n",
                        g_adc7606_data[0], g_adc7606_data[1]);
        }

        Delay_ms(500);
    }

    USART_Printf("G题综合测试完成！\r\n\r\n");
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  CMSIS-DSP库配置说明
  * @note   要使用FFT模块，需要在Keil项目中进行以下配置：
  *         1. 添加CMSIS-DSP库路径到Include Paths
  *         2. 在C/C++选项的Define中添加：ARM_MATH_CM4,ARM_MATH_MATRIX_CHECK,ARM_MATH_ROUNDING
  *         3. 在链接器中添加库文件：arm_cortexM4lf_math.lib
  *         4. 取消注释上面的#include "fft.h"
  */

#ifdef  USE_FULL_ASSERT
/**
  * @brief  assert_failed: 发生参数错误时进入此函数
  * @param  file: 源文件名
  * @param  line: 行号
  */
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    USART_Printf("ASSERT FAILED: %s:%lu\r\n", file, line);
    while (1) {}
}
#endif

/**
  * @brief  定时延时递减函数
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}
