/**
 * @file    project_verification.c
 * @brief   STM32F407电赛G题项目验证程序
 * <AUTHOR>
 * @date    2024
 * @note    用于验证所有驱动模块的正确性和兼容性
 */

#include "main.h"
#include "bsp.h"
#include "dac8552.h"
#include "ad7606.h"
#include "cd4052.h"
#include "systick.h"
#include "usart.h"

/**
 * @brief  项目完整性验证
 * @param  None
 * @retval 0: 验证通过, 1: 验证失败
 */
uint8_t Project_Verification(void)
{
    uint8_t result;
    uint8_t error_count = 0;
    
    USART_Printf("\r\n=== STM32F407电赛G题项目验证 ===\r\n");
    
    /* 1. BSP验证 */
    USART_Printf("1. BSP验证...\r\n");
    BSP_Init();
    USART_Printf("   BSP初始化: 通过\r\n");
    
    /* 2. DAC8552验证 */
    USART_Printf("2. DAC8552验证...\r\n");
    result = DAC8552_Init();
    if (result == DAC8552_OK) {
        USART_Printf("   DAC8552初始化: 通过\r\n");
        
        // 测试通道A
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, 2.5f);
        if (result == DAC8552_OK) {
            USART_Printf("   DAC8552通道A输出: 通过\r\n");
        } else {
            USART_Printf("   DAC8552通道A输出: 失败(%d)\r\n", result);
            error_count++;
        }
        
        // 测试通道B
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_B, 1.0f);
        if (result == DAC8552_OK) {
            USART_Printf("   DAC8552通道B输出: 通过\r\n");
        } else {
            USART_Printf("   DAC8552通道B输出: 失败(%d)\r\n", result);
            error_count++;
        }
    } else {
        USART_Printf("   DAC8552初始化: 失败(%d)\r\n", result);
        error_count++;
    }
    
    /* 3. CD4052验证 */
    USART_Printf("3. CD4052验证...\r\n");
    result = CD4052_Init();
    if (result == CD4052_OK) {
        USART_Printf("   CD4052初始化: 通过\r\n");
        
        // 测试所有增益级别
        for (uint8_t level = 0; level < 4; level++) {
            result = CD4052_SetGain(level);
            if (result == CD4052_OK) {
                uint8_t current_level = CD4052_GetGain();
                if (current_level == level) {
                    USART_Printf("   CD4052增益级别%d: 通过\r\n", level);
                } else {
                    USART_Printf("   CD4052增益级别%d: 状态不匹配\r\n", level);
                    error_count++;
                }
            } else {
                USART_Printf("   CD4052增益级别%d: 失败(%d)\r\n", level, result);
                error_count++;
            }
        }
    } else {
        USART_Printf("   CD4052初始化: 失败(%d)\r\n", result);
        error_count++;
    }
    
    /* 4. AD7606验证 */
    USART_Printf("4. AD7606验证...\r\n");
    result = AD7606_Init();
    if (result == AD7606_OK) {
        USART_Printf("   AD7606初始化: 通过\r\n");
        
        // 测试数据采集
        int16_t adc_data[AD7606_CHANNEL_COUNT];
        result = AD7606_ReadData(adc_data);
        if (result == AD7606_OK) {
            USART_Printf("   AD7606数据采集: 通过\r\n");
            USART_Printf("   采集数据: ");
            for (uint8_t i = 0; i < 4; i++) {
                USART_Printf("CH%d=%d ", i, adc_data[i]);
            }
            USART_Printf("\r\n");
        } else {
            USART_Printf("   AD7606数据采集: 失败(%d)\r\n", result);
            error_count++;
        }
    } else {
        USART_Printf("   AD7606初始化: 失败(%d)\r\n", result);
        error_count++;
    }
    
    /* 5. 系统集成验证 */
    USART_Printf("5. 系统集成验证...\r\n");
    
    // 综合测试：DAC输出 -> 增益控制 -> ADC采集
    result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, 3.0f);
    if (result == DAC8552_OK) {
        result = CD4052_SetGain(CD4052_GAIN_LEVEL_1);
        if (result == CD4052_OK) {
            SysTick_Delay_ms(10); // 等待系统稳定
            
            int16_t test_data[AD7606_CHANNEL_COUNT];
            result = AD7606_ReadData(test_data);
            if (result == AD7606_OK) {
                USART_Printf("   系统集成测试: 通过\r\n");
                USART_Printf("   集成测试结果: CH0=%d\r\n", test_data[0]);
            } else {
                USART_Printf("   系统集成测试: ADC读取失败\r\n");
                error_count++;
            }
        } else {
            USART_Printf("   系统集成测试: 增益设置失败\r\n");
            error_count++;
        }
    } else {
        USART_Printf("   系统集成测试: DAC设置失败\r\n");
        error_count++;
    }
    
    /* 6. 性能验证 */
    USART_Printf("6. 性能验证...\r\n");
    
    // 测试ADC采集速度
    uint32_t start_time = SysTick_GetTick();
    int16_t speed_test_data[AD7606_CHANNEL_COUNT];
    uint8_t success_count = 0;
    
    for (uint8_t i = 0; i < 10; i++) {
        result = AD7606_ReadData(speed_test_data);
        if (result == AD7606_OK) {
            success_count++;
        }
    }
    
    uint32_t elapsed_time = SysTick_GetTick() - start_time;
    float sample_rate = (success_count * 1000.0f) / elapsed_time;
    
    USART_Printf("   ADC采集性能: %d/10次成功, %.1f次/秒\r\n", success_count, sample_rate);
    
    if (success_count < 8) {
        USART_Printf("   性能验证: 采集成功率过低\r\n");
        error_count++;
    } else {
        USART_Printf("   性能验证: 通过\r\n");
    }
    
    /* 验证结果汇总 */
    USART_Printf("\r\n=== 验证结果汇总 ===\r\n");
    if (error_count == 0) {
        USART_Printf("✅ 项目验证通过！所有模块工作正常。\r\n");
        USART_Printf("🚀 系统已准备好进行电赛开发！\r\n");
        return 0;
    } else {
        USART_Printf("❌ 项目验证失败！发现 %d 个错误。\r\n", error_count);
        USART_Printf("🔧 请检查硬件连接和配置。\r\n");
        return 1;
    }
}

/**
 * @brief  硬件连接检查
 * @param  None
 * @retval None
 */
void Hardware_Connection_Check(void)
{
    USART_Printf("\r\n=== 硬件连接检查 ===\r\n");
    
    USART_Printf("请确认以下硬件连接：\r\n\r\n");
    
    USART_Printf("DAC8552 (SPI2):\r\n");
    USART_Printf("  SCK  -> PB13\r\n");
    USART_Printf("  MOSI -> PB15\r\n");
    USART_Printf("  SYNC -> PB12\r\n\r\n");
    
    USART_Printf("AD7606 (SPI1):\r\n");
    USART_Printf("  SCK    -> PA5\r\n");
    USART_Printf("  MISO   -> PA6\r\n");
    USART_Printf("  CS     -> PA15\r\n");
    USART_Printf("  CONVST -> PC7\r\n");
    USART_Printf("  BUSY   -> PC6\r\n");
    USART_Printf("  RESET  -> PC8\r\n\r\n");
    
    USART_Printf("CD4052:\r\n");
    USART_Printf("  A -> PE2\r\n");
    USART_Printf("  B -> PE3\r\n\r\n");
    
    USART_Printf("电源和地线连接正确\r\n");
    USART_Printf("所有芯片供电正常\r\n\r\n");
}
