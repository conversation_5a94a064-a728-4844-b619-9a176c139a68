#include "key.h"

#define keyms 200

void keyPinInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;

	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOG,ENABLE);
	
	GPIO_InitStructure.GPIO_Pin  = GPIO_Pin_4|GPIO_Pin_5|GPIO_Pin_6|GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOG,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0|GPIO_Pin_1|GPIO_Pin_2|GPIO_Pin_3;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOG,&GPIO_InitStructure);		
}

unsigned short int getKeyNum(void)
{
	while(1)
	{
		GPIO_ResetBits(GPIOG, GPIO_Pin_4);
		GPIO_SetBits(GPIOG, GPIO_Pin_5|GPIO_Pin_6|GPIO_Pin_7);
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_0) == 0){
			delay_ms(keyms);return 10;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_1) == 0){
			delay_ms(keyms);return 0;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_2) == 0){
			delay_ms(keyms);return 11;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_3) == 0){
			delay_ms(keyms);return 15;}
		
		GPIO_ResetBits(GPIOG, GPIO_Pin_5);
		GPIO_SetBits(GPIOG, GPIO_Pin_4|GPIO_Pin_6|GPIO_Pin_7);
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_0) == 0){
			delay_ms(keyms);return 1;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_1) == 0){
			delay_ms(keyms);return 2;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_2) == 0){
			delay_ms(keyms);return 3;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_3) == 0){
			delay_ms(keyms);return 14;}
		
		GPIO_ResetBits(GPIOG, GPIO_Pin_6);
		GPIO_SetBits(GPIOG, GPIO_Pin_5|GPIO_Pin_4|GPIO_Pin_7);
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_0) == 0){
			delay_ms(keyms);return 4;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_1) == 0){
			delay_ms(keyms);return 5;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_2) == 0){
			delay_ms(keyms);return 6;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_3) == 0){
			delay_ms(keyms);return 13;}
		
		GPIO_ResetBits(GPIOG, GPIO_Pin_7);
		GPIO_SetBits(GPIOG, GPIO_Pin_5|GPIO_Pin_6|GPIO_Pin_4);
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_0) == 0){
			delay_ms(keyms);return 7;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_1) == 0){
			delay_ms(keyms);return 8;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_2) == 0){
			delay_ms(keyms);return 9;}
		if(GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_3) == 0){
			delay_ms(keyms);return 12	;}
	}
}



