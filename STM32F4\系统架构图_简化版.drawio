<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="draw.io" version="22.1.16" type="device">
  <diagram name="STM32F407电路模型探究装置系统架构图" id="system-simple">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 主控制器 -->
        <mxCell id="mcu" value="STM32F407VGT6&#xa;主控制器&#xa;Cortex-M4 168MHz" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="300" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 信号生成模块 -->
        <mxCell id="dac-module" value="信号生成模块&#xa;DAC8552 + DDS算法&#xa;100Hz-1MHz正弦波" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="100" y="200" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 数据采集模块 -->
        <mxCell id="adc-module" value="数据采集模块&#xa;AD7606同步ADC&#xa;8通道200kSPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="720" y="200" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 增益控制模块 -->
        <mxCell id="gain-module" value="增益控制模块&#xa;CD4052开关&#xa;四档自动增益" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="720" y="400" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 被测电路 -->
        <mxCell id="dut" value="被测电路&#xa;DUT&#xa;待分析网络" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="100" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 人机接口 -->
        <mxCell id="hmi" value="人机接口&#xa;OLED显示+按键&#xa;串口调试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="100" y="400" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 信号处理模块 -->
        <mxCell id="dsp-module" value="信号处理模块&#xa;FFT+相位检测&#xa;频率响应计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="400" y="500" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- DAC到主控 -->
        <mxCell id="conn1" value="SPI2" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="dac-module" target="mcu">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 主控到ADC -->
        <mxCell id="conn2" value="SPI1" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mcu" target="adc-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 主控到增益控制 -->
        <mxCell id="conn3" value="GPIO" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mcu" target="gain-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 主控到人机接口 -->
        <mxCell id="conn4" value="I2C/UART" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mcu" target="hmi">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 主控到信号处理 -->
        <mxCell id="conn5" value="内部处理" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="mcu" target="dsp-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- DAC到被测电路 -->
        <mxCell id="conn6" value="激励信号" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="dac-module" target="dut">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 被测电路到增益控制 -->
        <mxCell id="conn7" value="响应信号" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="dut" target="gain-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 增益控制到ADC -->
        <mxCell id="conn8" value="调理信号" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="gain-module" target="adc-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 标题 -->
        <mxCell id="title" value="STM32F407电路模型探究装置系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="20" width="400" height="30" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例：&#xa;蓝色 - 人机接口&#xa;绿色 - 信号处理&#xa;橙色 - 控制模块&#xa;紫色 - 主控制器&#xa;红色 - 被测电路" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="600" width="150" height="100" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
