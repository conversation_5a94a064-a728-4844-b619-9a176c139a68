#include "bsp.h"
#include "systick.h"

/**
 * @brief  板级支持包初始化函数
 * @param  None
 * @retval None
 * @note   完成系统时钟和基础外设的初始化
 */
void BSP_Init(void)
{
    // 1. 配置系统时钟为168MHz (在system_stm32f4xx.c中已配置)
    
    // 2. 配置SysTick定时器为1ms中断
    // SystemCoreClock = 168MHz, 1ms = 168000 ticks
    if (SysTick_Config(SystemCoreClock / 1000))
    {
        // 配置失败，进入死循环
        while (1);
    }
    
    // 3. 设置SysTick中断优先级
    NVIC_SetPriority(SysTick_IRQn, 0);
    
    // 4. 使能所有GPIO端口时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);
    
    // 5. 使能SPI外设时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);  // AD7606
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_SPI2, ENABLE);  // DAC8552
    
    // 6. 初始化SysTick延时模块
    SysTick_Init();
}

/**
 * @brief  SysTick中断服务函数
 * @param  None
 * @retval None
 * @note   每1ms调用一次，用于系统时基
 */
void SysTick_Handler(void)
{
    // SysTick中断处理，由systick.c模块处理
    SysTick_Handler_Internal();
}
