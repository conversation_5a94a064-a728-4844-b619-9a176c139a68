Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to lcd.o(.text) for LCD_Init
    main.o(.text) refers to key.o(.text) for keyPinInit
    main.o(.text) refers to exit.o(.text) for exitInit
    main.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    main.o(.text) refers to tim.o(.text) for timCapInit
    main.o(.text) refers to ad9833.o(.text) for AD9833_Init_GPIO
    main.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    main.o(.text) refers to adc.o(.text) for adcGpioInit
    main.o(.text) refers to dma.o(.text) for dmaInit
    main.o(.text) refers to showosc.o(.text) for showOscUi
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetCapture1
    main.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(.text) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    main.o(.text) refers to lcd.o(.data) for BACK_COLOR
    main.o(.text) refers to showpar.o(.data) for wave
    main.o(.text) refers to main.o(.data) for upFlag2
    main.o(.text) refers to showosc.o(.data) for osc
    main.o(.text) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to exit.o(.text) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to tim.o(.text) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    ad9833.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9833.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9833.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9833.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9833.o(.text) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    lcd.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(.text) refers to delay.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    lcd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    lcd.o(.text) refers to stm32f4xx_fsmc.o(.text) for FSMC_NORSRAMInit
    lcd.o(.text) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_Cmd
    dma.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    dma.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_Init
    dma.o(.text) refers to dma.o(.bss) for ADC_ConvertedValue
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for delay_ms
    setpar.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    setpar.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    setpar.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    setpar.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    setpar.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    setpar.o(.text) refers to lcd.o(.text) for LCD_Draw_Picture
    setpar.o(.text) refers to key.o(.text) for getKeyNum
    setpar.o(.text) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    setpar.o(.text) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    setpar.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    setpar.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    setpar.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    setpar.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    setpar.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    setpar.o(.text) refers to ad9833.o(.text) for AD9833_WaveSeting
    setpar.o(.text) refers to noretval__2printf.o(.text) for __2printf
    setpar.o(.text) refers to lcd.o(.data) for POINT_COLOR
    setpar.o(.text) refers to setpar.o(.constdata) for gImage_keyboard
    setpar.o(.text) refers to showpar.o(.data) for wave
    setpar.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    setpar.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    setpar.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    setpar.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    showpar.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    showpar.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    showpar.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    showpar.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    showpar.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    showpar.o(.text) refers to noretval__2printf.o(.text) for __2printf
    showpar.o(.text) refers to lcd.o(.text) for LCD_Display_Dir
    showpar.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    showpar.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    showpar.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    showpar.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    showpar.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    showpar.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    showpar.o(.text) refers to showpar.o(.data) for wave
    showpar.o(.text) refers to lcd.o(.data) for BACK_COLOR
    showpar.o(.text) refers to showpar.o(.constdata) for gImage_ddsParTitle
    showpar.o(.text) refers to key.o(.text) for getKeyNum
    showpar.o(.text) refers to showosc.o(.text) for showOscUi
    showpar.o(.text) refers to setpar.o(.text) for ddsSetWindow
    oscuint.o(.text) refers to lcd.o(.text) for LCD_DrawLine
    oscuint.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oscuint.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oscuint.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oscuint.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oscuint.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oscuint.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oscuint.o(.text) refers to showosc.o(.data) for osc
    oscuint.o(.text) refers to lcd.o(.data) for BACK_COLOR
    oscuint.o(.text) refers to dma.o(.bss) for ADC_ConvertedValue
    oscuint.o(.text) refers to main.o(.data) for minVol
    showosc.o(.text) refers to lcd.o(.text) for LCD_Draw_Picture
    showosc.o(.text) refers to oscuint.o(.text) for osc_Background
    showosc.o(.text) refers to lcd.o(.data) for BACK_COLOR
    showosc.o(.text) refers to showosc.o(.constdata) for gImage_osclogo
    showosc.o(.text) refers to showosc.o(.data) for osc
    tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    tim.o(.text) refers to misc.o(.text) for NVIC_Init
    tim.o(.text) refers to oscuint.o(.text) for osc_Wave
    tim.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tim.o(.text) refers to tim.o(.bss) for TIM5_ICInitStructure
    tim.o(.text) refers to main.o(.data) for upFlag1
    exit.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    exit.o(.text) refers to stm32f4xx_syscfg.o(.text) for SYSCFG_EXTILineConfig
    exit.o(.text) refers to stm32f4xx_exti.o(.text) for EXTI_Init
    exit.o(.text) refers to misc.o(.text) for NVIC_Init
    exit.o(.text) refers to delay.o(.text) for delay_ms
    exit.o(.text) refers to lcd.o(.text) for LCD_Clear
    exit.o(.text) refers to showpar.o(.text) for ddsShowWindow
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.constdata), (48000 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing setpar.o(.rev16_text), (4 bytes).
    Removing setpar.o(.revsh_text), (4 bytes).
    Removing showpar.o(.rev16_text), (4 bytes).
    Removing showpar.o(.revsh_text), (4 bytes).
    Removing oscuint.o(.rev16_text), (4 bytes).
    Removing oscuint.o(.revsh_text), (4 bytes).
    Removing showosc.o(.rev16_text), (4 bytes).
    Removing showosc.o(.revsh_text), (4 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing exit.o(.rev16_text), (4 bytes).
    Removing exit.o(.revsh_text), (4 bytes).

131 unused section(s) (total 71950 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\AD9833\ad9833.c                       0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\ADC_DMA\adc.c                         0x00000000   Number         0  adc.o ABSOLUTE
    ..\ADC_DMA\dma.c                         0x00000000   Number         0  dma.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\EXIT\exit.c                           0x00000000   Number         0  exit.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\Matrix_keys\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TFTLCD\lcd.c                          0x00000000   Number         0  lcd.o ABSOLUTE
    ..\TIM\tim.c                             0x00000000   Number         0  tim.o ABSOLUTE
    ..\\AD9833\\ad9833.c                     0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\ADC_DMA\\adc.c                       0x00000000   Number         0  adc.o ABSOLUTE
    ..\\ADC_DMA\\dma.c                       0x00000000   Number         0  dma.o ABSOLUTE
    ..\\EXIT\\exit.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\Matrix_keys\\key.c                   0x00000000   Number         0  key.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TFTLCD\\lcd.c                        0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\TIM\\tim.c                           0x00000000   Number         0  tim.o ABSOLUTE
    ..\\dds_ui\\setPar.c                     0x00000000   Number         0  setpar.o ABSOLUTE
    ..\\dds_ui\\showPar.c                    0x00000000   Number         0  showpar.o ABSOLUTE
    ..\\osc_ui\\oscUint.c                    0x00000000   Number         0  oscuint.o ABSOLUTE
    ..\\osc_ui\\showOsc.c                    0x00000000   Number         0  showosc.o ABSOLUTE
    ..\dds_ui\setPar.c                       0x00000000   Number         0  setpar.o ABSOLUTE
    ..\dds_ui\showPar.c                      0x00000000   Number         0  showpar.o ABSOLUTE
    ..\osc_ui\oscUint.c                      0x00000000   Number         0  oscuint.o ABSOLUTE
    ..\osc_ui\showOsc.c                      0x00000000   Number         0  showosc.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000208   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000212   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000214   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000218   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800021e   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000228   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800022a   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800022c   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800022e   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800022e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800022e   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000234   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000238   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000238   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000240   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000242   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000242   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000246   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800024c   Section        0  main.o(.text)
    .text                                    0x080009f4   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000a10   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000a11   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000c20   Section        0  delay.o(.text)
    .text                                    0x08000d24   Section        0  usart.o(.text)
    .text                                    0x08000e6c   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000e6c   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000eac   Section        0  misc.o(.text)
    .text                                    0x08000f8c   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x080013f0   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x08001798   Section        0  stm32f4xx_exti.o(.text)
    .text                                    0x080018a8   Section        0  stm32f4xx_fsmc.o(.text)
    .text                                    0x08001e70   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08002104   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08002760   Section        0  stm32f4xx_syscfg.o(.text)
    .text                                    0x080027f4   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x08002f93   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08002ff5   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08003057   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x080030c3   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08003498   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x080038ec   Section        0  ad9833.o(.text)
    AD9833_Delay                             0x0800391d   Thumb Code    14  ad9833.o(.text)
    .text                                    0x08003af0   Section        0  lcd.o(.text)
    .text                                    0x080087f4   Section        0  adc.o(.text)
    .text                                    0x080088d4   Section        0  dma.o(.text)
    .text                                    0x08008944   Section        0  key.o(.text)
    .text                                    0x08008b1c   Section        0  setpar.o(.text)
    .text                                    0x08009b40   Section        0  showpar.o(.text)
    .text                                    0x0800a26c   Section        0  oscuint.o(.text)
    .text                                    0x0800a6ec   Section        0  showosc.o(.text)
    .text                                    0x0800a890   Section        0  tim.o(.text)
    .text                                    0x0800aa28   Section        0  exit.o(.text)
    .text                                    0x0800aa90   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800aa94   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800aaac   Section        0  _printf_dec.o(.text)
    .text                                    0x0800ab24   Section        0  _printf_hex_int.o(.text)
    .text                                    0x0800ab7c   Section        0  __printf_wp.o(.text)
    .text                                    0x0800ac8a   Section        0  heapauxi.o(.text)
    .text                                    0x0800ac90   Section        2  use_no_semi.o(.text)
    .text                                    0x0800ac92   Section        0  _rserrno.o(.text)
    .text                                    0x0800aca8   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800ad5a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800ad5d   Thumb Code   428  _printf_fp_dec.o(.text)
    .text                                    0x0800b174   Section        0  _printf_char_file.o(.text)
    .text                                    0x0800b198   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800b1a0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800b1a8   Section      138  lludiv10.o(.text)
    .text                                    0x0800b234   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800b235   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800b264   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x0800b2e4   Section        0  bigflt0.o(.text)
    .text                                    0x0800b3c0   Section        0  ferror.o(.text)
    .text                                    0x0800b3c8   Section        8  libspace.o(.text)
    .text                                    0x0800b3d0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800b41a   Section        0  exit.o(.text)
    .text                                    0x0800b42c   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x0800b4ac   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800b4ea   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800b530   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800b590   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800b8c8   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800b98e   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800b9b6   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800b9de   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x0800bc22   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_pow                           0x0800bc58   Section        0  pow.o(i.__hardfp_pow)
    i.__kernel_poly                          0x0800c8a8   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x0800c9a0   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x0800c9d0   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x0800c9e8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x0800ca08   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x0800ca28   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x0800ca48   Section        0  __printf_wp.o(i._is_digit)
    i.fabs                                   0x0800ca56   Section        0  fabs.o(i.fabs)
    i.sqrt                                   0x0800ca6e   Section        0  sqrt.o(i.sqrt)
    locale$$code                             0x0800cadc   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x0800cb08   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x0800cb08   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800cb20   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800cb20   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x0800cb84   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x0800cb84   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800cb95   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x0800ccd4   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x0800ccd4   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x0800cce4   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800cce4   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x0800ccfc   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x0800ccfc   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800cd03   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x0800cfac   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800cfac   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x0800d00c   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x0800d00c   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800d066   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800d066   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x0800d094   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800d094   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x0800d0bc   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x0800d0bc   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800d134   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800d134   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800d288   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800d288   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800d324   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800d324   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800d330   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800d330   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800d39c   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800d39c   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800d3b4   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800d3b4   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800d54c   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800d54c   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800d55d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fpinit                             0x0800d720   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800d720   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800d72a   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800d72a   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800d734   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800d734   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x0800d738   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x0800d738   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800d79c   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x0800d79c   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x0800d7f8   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800d7f8   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800d828   Section       28  stm32f4xx_fsmc.o(.constdata)
    x$fpl$usenofp                            0x0800d828   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800d844   Section     6080  lcd.o(.constdata)
    .constdata                               0x0800f004   Section     3980  setpar.o(.constdata)
    .constdata                               0x0800ff90   Section     3780  showpar.o(.constdata)
    .constdata                               0x08010e54   Section     6930  showosc.o(.constdata)
    .constdata                               0x08012966   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x08012966   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0801297a   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08012990   Section      136  pow.o(.constdata)
    bp                                       0x08012990   Data          16  pow.o(.constdata)
    dp_h                                     0x080129a0   Data          16  pow.o(.constdata)
    dp_l                                     0x080129b0   Data          16  pow.o(.constdata)
    L                                        0x080129c0   Data          48  pow.o(.constdata)
    P                                        0x080129f0   Data          40  pow.o(.constdata)
    .constdata                               0x08012a18   Section        8  qnan.o(.constdata)
    .constdata                               0x08012a20   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08012a20   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08012a5c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08012ad4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08012ad8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08012ae0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08012aec   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08012aee   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08012aef   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08012af0   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       40  main.o(.data)
    .data                                    0x20000028   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000003c   Section        4  delay.o(.data)
    fac_us                                   0x2000003c   Data           1  delay.o(.data)
    fac_ms                                   0x2000003e   Data           2  delay.o(.data)
    .data                                    0x20000040   Section        6  usart.o(.data)
    .data                                    0x20000046   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000046   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000056   Section        4  lcd.o(.data)
    .data                                    0x20000060   Section       24  showpar.o(.data)
    .data                                    0x20000078   Section       58  showosc.o(.data)
    .bss                                     0x200000b4   Section      200  usart.o(.bss)
    .bss                                     0x2000017c   Section       14  lcd.o(.bss)
    .bss                                     0x2000018a   Section     1200  dma.o(.bss)
    .bss                                     0x2000063a   Section       10  tim.o(.bss)
    .bss                                     0x20000644   Section       96  libspace.o(.bss)
    HEAP                                     0x200006a8   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200006a8   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200008a8   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200008a8   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000ca8   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x08000209   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x0800020f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000213   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800022b   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800022f   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800022f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800022f   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000241   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000247   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x0800024d   Thumb Code  1910  main.o(.text)
    NMI_Handler                              0x080009f5   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080009f7   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080009fb   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080009ff   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000a03   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x08000a07   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000a09   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000a0b   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000a0d   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000aed   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000b45   Thumb Code   174  system_stm32f4xx.o(.text)
    delay_init                               0x08000c21   Thumb Code    52  delay.o(.text)
    delay_us                                 0x08000c55   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08000c9d   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08000ce5   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x08000d25   Thumb Code     4  usart.o(.text)
    fputc                                    0x08000d29   Thumb Code    22  usart.o(.text)
    uart_init                                0x08000d3f   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x08000de3   Thumb Code   122  usart.o(.text)
    Reset_Handler                            0x08000e6d   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000e87   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000e89   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000ead   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08000eb7   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08000f21   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08000f2f   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08000f51   Thumb Code    40  misc.o(.text)
    ADC_DeInit                               0x08000f8d   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x08000fa3   Thumb Code    74  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x08000fed   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x08001001   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x08001023   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800102f   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x08001045   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x08001055   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x0800105b   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x0800106b   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x0800108d   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x080010af   Thumb Code   184  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x08001167   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x08001171   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x08001185   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x0800119b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x080011b1   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x080011c9   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x080011df   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x080011e7   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x080011ef   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x08001205   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x0800121b   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x0800123d   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x080012bf   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x080012d7   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x080012eb   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x080012fb   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x0800130b   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x08001315   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x08001329   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x0800133f   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x08001355   Thumb Code    28  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x08001371   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x080013a9   Thumb Code    18  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x080013bb   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x080013c1   Thumb Code    38  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x080013e7   Thumb Code    10  stm32f4xx_adc.o(.text)
    DMA_DeInit                               0x080013f1   Thumb Code   324  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x08001535   Thumb Code    82  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x08001587   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x080015a9   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x080015bf   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x080015d5   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x080015eb   Thumb Code     4  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x080015ef   Thumb Code     8  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x080015f7   Thumb Code    24  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x0800160f   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x08001625   Thumb Code    10  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x0800162f   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x08001643   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x08001657   Thumb Code    12  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x08001663   Thumb Code    56  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x0800169b   Thumb Code    40  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x080016c3   Thumb Code    58  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x080016fd   Thumb Code    84  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x08001751   Thumb Code    40  stm32f4xx_dma.o(.text)
    EXTI_DeInit                              0x08001799   Thumb Code    28  stm32f4xx_exti.o(.text)
    EXTI_Init                                0x080017b5   Thumb Code   142  stm32f4xx_exti.o(.text)
    EXTI_StructInit                          0x08001843   Thumb Code    16  stm32f4xx_exti.o(.text)
    EXTI_GenerateSWInterrupt                 0x08001853   Thumb Code    16  stm32f4xx_exti.o(.text)
    EXTI_GetFlagStatus                       0x08001863   Thumb Code    22  stm32f4xx_exti.o(.text)
    EXTI_ClearFlag                           0x08001879   Thumb Code     8  stm32f4xx_exti.o(.text)
    EXTI_GetITStatus                         0x08001881   Thumb Code    22  stm32f4xx_exti.o(.text)
    EXTI_ClearITPendingBit                   0x08001897   Thumb Code     8  stm32f4xx_exti.o(.text)
    FSMC_NORSRAMDeInit                       0x080018a9   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMInit                         0x080018df   Thumb Code   230  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMStructInit                   0x080019c5   Thumb Code    48  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMCmd                          0x080019f5   Thumb Code    46  stm32f4xx_fsmc.o(.text)
    FSMC_NANDDeInit                          0x08001a23   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_NANDInit                            0x08001a61   Thumb Code   132  stm32f4xx_fsmc.o(.text)
    FSMC_NANDStructInit                      0x08001ae5   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NANDCmd                             0x08001b1b   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_NANDECCCmd                          0x08001b71   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_GetECC                              0x08001bc7   Thumb Code    24  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDDeInit                        0x08001bdf   Thumb Code    36  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDInit                          0x08001c03   Thumb Code   130  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDStructInit                    0x08001c85   Thumb Code    60  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDCmd                           0x08001cc1   Thumb Code    44  stm32f4xx_fsmc.o(.text)
    FSMC_ITConfig                            0x08001ced   Thumb Code   128  stm32f4xx_fsmc.o(.text)
    FSMC_GetFlagStatus                       0x08001d6d   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_ClearFlag                           0x08001da3   Thumb Code    74  stm32f4xx_fsmc.o(.text)
    FSMC_GetITStatus                         0x08001ded   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_ClearITPendingBit                   0x08001e2b   Thumb Code    66  stm32f4xx_fsmc.o(.text)
    GPIO_DeInit                              0x08001e71   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x08001f7d   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800200d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800201f   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08002041   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08002053   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800205b   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800206d   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08002075   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08002079   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800207d   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08002087   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800208b   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08002093   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08002105   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08002157   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08002165   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080021a1   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080021d9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080021ed   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080021f3   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08002221   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08002227   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08002247   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800224d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800225b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08002261   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08002275   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800227b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08002281   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x0800229d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080022b9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080022cd   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x080022d9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x080022ed   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08002301   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08002317   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080023f5   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800242b   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08002433   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800243b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08002441   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800245b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08002477   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800248b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x0800249f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080024b3   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080024b9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x080024db   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08002529   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800254b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x0800256d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x0800258f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x080025b1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080025d3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080025f5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08002617   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08002639   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x0800265b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x0800267d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x0800269f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x080026c1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x080026e3   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x0800270b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x0800272d   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x0800273f   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08002755   Thumb Code     8  stm32f4xx_rcc.o(.text)
    SYSCFG_DeInit                            0x08002761   Thumb Code    22  stm32f4xx_syscfg.o(.text)
    SYSCFG_MemoryRemapConfig                 0x08002777   Thumb Code     6  stm32f4xx_syscfg.o(.text)
    SYSCFG_MemorySwappingBank                0x0800277d   Thumb Code     6  stm32f4xx_syscfg.o(.text)
    SYSCFG_EXTILineConfig                    0x08002783   Thumb Code    66  stm32f4xx_syscfg.o(.text)
    SYSCFG_ETH_MediaInterfaceConfig          0x080027c5   Thumb Code     8  stm32f4xx_syscfg.o(.text)
    SYSCFG_CompensationCellCmd               0x080027cd   Thumb Code     6  stm32f4xx_syscfg.o(.text)
    SYSCFG_GetCompensationCellStatus         0x080027d3   Thumb Code    22  stm32f4xx_syscfg.o(.text)
    TIM_DeInit                               0x080027f5   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800294f   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080029b7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x080029c9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080029cf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x080029e1   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x080029e5   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x080029e9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x080029ef   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080029f5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08002a0d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08002a25   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08002a3d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x08002a4f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08002a61   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08002a79   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x08002aeb   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08002b85   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08002c51   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x08002cc1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08002cd5   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08002d2b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x08002d2f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x08002d33   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08002d37   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08002d3b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08002d4d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08002d67   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08002d79   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08002d93   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08002da5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08002dbf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08002dd1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08002deb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08002dfd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08002e17   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08002e29   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08002e43   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08002e55   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08002e6d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08002e7f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08002e97   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08002ea9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08002ebb   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08002ed5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08002eef   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08002f09   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08002f23   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08002f3d   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08002f5b   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08002f79   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08002fe3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x0800303d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080030b1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x080030fd   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x0800316b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x0800317d   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x080031f9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x080031ff   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08003205   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x0800320b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08003211   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08003231   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08003243   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08003261   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08003279   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08003291   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x080032a3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x080032a7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x080032b9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x080032bf   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x080032e1   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x080032e7   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x080032f1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08003303   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0800331b   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08003327   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08003339   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08003351   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x0800338f   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x080033ab   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x080033e1   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08003401   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08003413   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08003425   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08003437   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08003479   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08003491   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08003499   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08003567   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08003633   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x0800364b   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x0800366b   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08003677   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x0800368f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x0800369f   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x080036b5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x080036cd   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x080036d5   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x080036df   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x080036f1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08003709   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x0800371b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x0800372d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08003745   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x0800374f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08003767   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08003777   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x0800378f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x080037a7   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x080037b9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x080037d1   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x080037e3   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x0800382d   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08003847   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08003859   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x080038cf   Thumb Code    30  stm32f4xx_usart.o(.text)
    AD9833_Init_GPIO                         0x080038ed   Thumb Code    48  ad9833.o(.text)
    AD9833_Write                             0x0800392b   Thumb Code   112  ad9833.o(.text)
    AD9833_AmpSet                            0x0800399b   Thumb Code   102  ad9833.o(.text)
    AD9833_WaveSeting                        0x08003a01   Thumb Code   220  ad9833.o(.text)
    LCD_WR_REG                               0x08003af1   Thumb Code    18  lcd.o(.text)
    LCD_WR_DATA                              0x08003b03   Thumb Code    20  lcd.o(.text)
    LCD_RD_DATA                              0x08003b17   Thumb Code    16  lcd.o(.text)
    LCD_WriteReg                             0x08003b27   Thumb Code    14  lcd.o(.text)
    LCD_ReadReg                              0x08003b35   Thumb Code    22  lcd.o(.text)
    LCD_WriteRAM_Prepare                     0x08003b4b   Thumb Code    10  lcd.o(.text)
    LCD_WriteRAM                             0x08003b55   Thumb Code     8  lcd.o(.text)
    LCD_BGR2RGB                              0x08003b5d   Thumb Code    26  lcd.o(.text)
    opt_delay                                0x08003b77   Thumb Code    14  lcd.o(.text)
    LCD_SetCursor                            0x08003b85   Thumb Code   418  lcd.o(.text)
    LCD_ReadPoint                            0x08003d27   Thumb Code   360  lcd.o(.text)
    LCD_DisplayOn                            0x08003e8f   Thumb Code   102  lcd.o(.text)
    LCD_DisplayOff                           0x08003ef5   Thumb Code    90  lcd.o(.text)
    LCD_Scan_Dir                             0x08003f4f   Thumb Code   744  lcd.o(.text)
    LCD_DrawPoint                            0x08004237   Thumb Code    28  lcd.o(.text)
    LCD_Fast_DrawPoint                       0x08004253   Thumb Code   388  lcd.o(.text)
    LCD_SSD_BackLightSet                     0x080043d7   Thumb Code    88  lcd.o(.text)
    LCD_Display_Dir                          0x0800442f   Thumb Code   444  lcd.o(.text)
    LCD_Set_Window                           0x080045eb   Thumb Code   546  lcd.o(.text)
    LCD_Clear                                0x0800480d   Thumb Code   100  lcd.o(.text)
    LCD_Init                                 0x08004871   Thumb Code 14696  lcd.o(.text)
    LCD_Fill                                 0x080081d9   Thumb Code   180  lcd.o(.text)
    LCD_Color_Fill                           0x0800828d   Thumb Code   100  lcd.o(.text)
    LCD_DrawLine                             0x080082f1   Thumb Code   176  lcd.o(.text)
    LCD_DrawRectangle                        0x080083a1   Thumb Code    60  lcd.o(.text)
    LCD_Draw_Circle                          0x080083dd   Thumb Code   152  lcd.o(.text)
    LCD_ShowChar                             0x08008475   Thumb Code   272  lcd.o(.text)
    LCD_Pow                                  0x08008585   Thumb Code    22  lcd.o(.text)
    LCD_ShowNum                              0x0800859b   Thumb Code   148  lcd.o(.text)
    LCD_ShowxNum                             0x0800862f   Thumb Code   220  lcd.o(.text)
    LCD_ShowString                           0x0800870b   Thumb Code   102  lcd.o(.text)
    LCD_Draw_Picture                         0x08008771   Thumb Code   122  lcd.o(.text)
    adcInit                                  0x080087f5   Thumb Code   116  adc.o(.text)
    adcGpioInit                              0x08008869   Thumb Code    36  adc.o(.text)
    getDataCmd                               0x0800888d   Thumb Code    60  adc.o(.text)
    dmaInit                                  0x080088d5   Thumb Code    98  dma.o(.text)
    keyPinInit                               0x08008945   Thumb Code    78  key.o(.text)
    getKeyNum                                0x08008993   Thumb Code   390  key.o(.text)
    keyBoard                                 0x08008b1d   Thumb Code   766  setpar.o(.text)
    setShape                                 0x08008e1b   Thumb Code   350  setpar.o(.text)
    setAmp                                   0x08008f79   Thumb Code   536  setpar.o(.text)
    setFreq                                  0x08009191   Thumb Code  1298  setpar.o(.text)
    setPhase                                 0x080096a3   Thumb Code   672  setpar.o(.text)
    ddsSetWindow                             0x08009943   Thumb Code   452  setpar.o(.text)
    ddsParShow                               0x08009b41   Thumb Code  1766  showpar.o(.text)
    ddsShowWindow                            0x0800a227   Thumb Code    48  showpar.o(.text)
    osc_Wave                                 0x0800a26d   Thumb Code   492  oscuint.o(.text)
    osc_Axes                                 0x0800a459   Thumb Code   346  oscuint.o(.text)
    osc_Background                           0x0800a5b3   Thumb Code    78  oscuint.o(.text)
    osc_Par                                  0x0800a601   Thumb Code   234  oscuint.o(.text)
    showOscUi                                0x0800a6ed   Thumb Code   318  showosc.o(.text)
    timInit                                  0x0800a891   Thumb Code    92  tim.o(.text)
    TIM3_IRQHandler                          0x0800a8ed   Thumb Code    28  tim.o(.text)
    timCapInit                               0x0800a909   Thumb Code   168  tim.o(.text)
    TIM5_IRQHandler                          0x0800a9b1   Thumb Code    90  tim.o(.text)
    exitInit                                 0x0800aa29   Thumb Code    76  exit.o(.text)
    EXTI0_IRQHandler                         0x0800aa75   Thumb Code    28  exit.o(.text)
    __use_no_semihosting                     0x0800aa91   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800aa95   Thumb Code    20  noretval__2printf.o(.text)
    _printf_int_dec                          0x0800aaad   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800ab25   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800ab25   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x0800ab7d   Thumb Code   270  __printf_wp.o(.text)
    __use_two_region_memory                  0x0800ac8b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800ac8d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800ac8f   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800ac91   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800ac91   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0800ac93   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800ac9d   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800aca9   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800ad5b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800af09   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_file                        0x0800b175   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x0800b199   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x0800b1a1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800b1a1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800b1a1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x0800b1a9   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x0800b23f   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x0800b265   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x0800b2e5   Thumb Code   216  bigflt0.o(.text)
    ferror                                   0x0800b3c1   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x0800b3c9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800b3c9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800b3c9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800b3d1   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800b41b   Thumb Code    18  exit.o(.text)
    strcmp                                   0x0800b42d   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x0800b4ad   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800b4eb   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800b531   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800b591   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800b8c9   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800b98f   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800b9b7   Thumb Code    40  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800b9df   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x0800bc23   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_pow                             0x0800bc59   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __kernel_poly                            0x0800c8a9   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x0800c9a1   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x0800c9d1   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x0800c9e9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x0800ca09   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x0800ca29   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x0800ca49   Thumb Code    14  __printf_wp.o(i._is_digit)
    fabs                                     0x0800ca57   Thumb Code    24  fabs.o(i.fabs)
    sqrt                                     0x0800ca6f   Thumb Code   110  sqrt.o(i.sqrt)
    _get_lc_numeric                          0x0800cadd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x0800cb09   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x0800cb09   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800cb0f   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800cb0f   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x0800cb15   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x0800cb1b   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800cb21   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800cb21   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x0800cb85   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800cb85   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x0800ccd5   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x0800cce5   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x0800ccfd   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x0800ccfd   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x0800cfad   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800cfad   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x0800d00d   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800d00d   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800d067   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800d067   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x0800d095   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800d095   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x0800d0bd   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800d0bd   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800d11f   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800d135   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800d135   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800d289   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800d325   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800d331   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800d331   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800d39d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800d39d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800d3b5   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800d54d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800d54d   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    _fp_init                                 0x0800d721   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800d729   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800d729   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800d72b   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800d735   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x0800d739   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800d79d   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x0800d7f9   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    FSMC_DefaultTimingStruct                 0x0800d828   Data          28  stm32f4xx_fsmc.o(.constdata)
    __I$use$fp                               0x0800d828   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800d844   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800dcb8   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800e2a8   Data        3420  lcd.o(.constdata)
    gImage_ddsSetTitle                       0x0800f004   Data        3780  setpar.o(.constdata)
    gImage_keyboard                          0x0800fec8   Data         200  setpar.o(.constdata)
    gImage_ddsParTitle                       0x0800ff90   Data        3780  showpar.o(.constdata)
    gImage_osctitle                          0x08010e54   Data        6300  showosc.o(.constdata)
    gImage_osclogo                           0x080126f0   Data         630  showosc.o(.constdata)
    __mathlib_zero                           0x08012a18   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08012ab4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08012ad4   Number         0  anon$$obj.o(Region$$Table)
    waveFreq                                 0x20000000   Data           4  main.o(.data)
    minVol                                   0x20000004   Data           2  main.o(.data)
    maxVol                                   0x20000006   Data           2  main.o(.data)
    cycNum                                   0x20000008   Data           4  main.o(.data)
    upTime                                   0x2000000c   Data           4  main.o(.data)
    upFlag1                                  0x20000010   Data           2  main.o(.data)
    upFlag2                                  0x20000012   Data           2  main.o(.data)
    strVol                                   0x20000014   Data           8  main.o(.data)
    strFreq                                  0x2000001c   Data          12  main.o(.data)
    SystemCoreClock                          0x20000028   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000002c   Data          16  system_stm32f4xx.o(.data)
    __stdout                                 0x20000040   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000044   Data           2  usart.o(.data)
    POINT_COLOR                              0x20000056   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000058   Data           2  lcd.o(.data)
    wave                                     0x20000060   Data          24  showpar.o(.data)
    osc                                      0x20000078   Data          28  showosc.o(.data)
    par_freq                                 0x20000094   Data           6  showosc.o(.data)
    par_maxV                                 0x2000009a   Data           6  showosc.o(.data)
    par_minV                                 0x200000a0   Data           6  showosc.o(.data)
    par_vppV                                 0x200000a6   Data           6  showosc.o(.data)
    par_relV                                 0x200000ac   Data           6  showosc.o(.data)
    USART_RX_BUF                             0x200000b4   Data         200  usart.o(.bss)
    lcddev                                   0x2000017c   Data          14  lcd.o(.bss)
    ADC_ConvertedValue                       0x2000018a   Data        1200  dma.o(.bss)
    TIM5_ICInitStructure                     0x2000063a   Data          10  tim.o(.bss)
    __libspace_start                         0x20000644   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200006a4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00012ba4, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00012af0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          326    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1395  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1719    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1721    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1723    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1392    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1391    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1390    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1389    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1456    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000212   0x08000212   0x00000002   Code   RO         1592    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000214   0x08000214   0x00000004   Code   RO         1593    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1596    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1599    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1601    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1603    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000006   Code   RO         1604    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1606    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1608    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1610    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x0000000a   Code   RO         1611    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1612    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1614    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1616    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1618    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1620    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1622    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1624    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1626    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1630    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1632    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1634    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1636    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000002   Code   RO         1637    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000002   Code   RO         1669    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1678    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1680    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1683    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1686    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1688    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1691    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000002   Code   RO         1692    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1447    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1508    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000006   Code   RO         1520    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1510    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000004   Code   RO         1511    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1513    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000008   Code   RO         1514    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000002   Code   RO         1640    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1649    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000242   0x08000242   0x00000004   Code   RO         1650    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000246   0x08000246   0x00000006   Code   RO         1651    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x000007a8   Code   RO            3    .text               main.o
    0x080009f4   0x080009f4   0x0000001a   Code   RO          180    .text               stm32f4xx_it.o
    0x08000a0e   0x08000a0e   0x00000002   PAD
    0x08000a10   0x08000a10   0x00000210   Code   RO          203    .text               system_stm32f4xx.o
    0x08000c20   0x08000c20   0x00000104   Code   RO          229    .text               delay.o
    0x08000d24   0x08000d24   0x00000148   Code   RO          295    .text               usart.o
    0x08000e6c   0x08000e6c   0x00000040   Code   RO          327    .text               startup_stm32f40_41xxx.o
    0x08000eac   0x08000eac   0x000000e0   Code   RO          333    .text               misc.o
    0x08000f8c   0x08000f8c   0x00000464   Code   RO          353    .text               stm32f4xx_adc.o
    0x080013f0   0x080013f0   0x000003a8   Code   RO          553    .text               stm32f4xx_dma.o
    0x08001798   0x08001798   0x00000110   Code   RO          596    .text               stm32f4xx_exti.o
    0x080018a8   0x080018a8   0x000005c8   Code   RO          659    .text               stm32f4xx_fsmc.o
    0x08001e70   0x08001e70   0x00000294   Code   RO          682    .text               stm32f4xx_gpio.o
    0x08002104   0x08002104   0x0000065c   Code   RO          845    .text               stm32f4xx_rcc.o
    0x08002760   0x08002760   0x00000094   Code   RO          970    .text               stm32f4xx_syscfg.o
    0x080027f4   0x080027f4   0x00000ca2   Code   RO          990    .text               stm32f4xx_tim.o
    0x08003496   0x08003496   0x00000002   PAD
    0x08003498   0x08003498   0x00000454   Code   RO         1010    .text               stm32f4xx_usart.o
    0x080038ec   0x080038ec   0x00000204   Code   RO         1050    .text               ad9833.o
    0x08003af0   0x08003af0   0x00004d04   Code   RO         1070    .text               lcd.o
    0x080087f4   0x080087f4   0x000000e0   Code   RO         1112    .text               adc.o
    0x080088d4   0x080088d4   0x00000070   Code   RO         1132    .text               dma.o
    0x08008944   0x08008944   0x000001d8   Code   RO         1155    .text               key.o
    0x08008b1c   0x08008b1c   0x00001024   Code   RO         1175    .text               setpar.o
    0x08009b40   0x08009b40   0x0000072c   Code   RO         1213    .text               showpar.o
    0x0800a26c   0x0800a26c   0x0000047e   Code   RO         1243    .text               oscuint.o
    0x0800a6ea   0x0800a6ea   0x00000002   PAD
    0x0800a6ec   0x0800a6ec   0x000001a4   Code   RO         1267    .text               showosc.o
    0x0800a890   0x0800a890   0x00000198   Code   RO         1294    .text               tim.o
    0x0800aa28   0x0800aa28   0x00000068   Code   RO         1318    .text               exit.o
    0x0800aa90   0x0800aa90   0x00000002   Code   RO         1338    .text               c_w.l(use_no_semi_2.o)
    0x0800aa92   0x0800aa92   0x00000002   PAD
    0x0800aa94   0x0800aa94   0x00000018   Code   RO         1342    .text               c_w.l(noretval__2printf.o)
    0x0800aaac   0x0800aaac   0x00000078   Code   RO         1346    .text               c_w.l(_printf_dec.o)
    0x0800ab24   0x0800ab24   0x00000058   Code   RO         1351    .text               c_w.l(_printf_hex_int.o)
    0x0800ab7c   0x0800ab7c   0x0000010e   Code   RO         1377    .text               c_w.l(__printf_wp.o)
    0x0800ac8a   0x0800ac8a   0x00000006   Code   RO         1393    .text               c_w.l(heapauxi.o)
    0x0800ac90   0x0800ac90   0x00000002   Code   RO         1445    .text               c_w.l(use_no_semi.o)
    0x0800ac92   0x0800ac92   0x00000016   Code   RO         1448    .text               c_w.l(_rserrno.o)
    0x0800aca8   0x0800aca8   0x000000b2   Code   RO         1450    .text               c_w.l(_printf_intcommon.o)
    0x0800ad5a   0x0800ad5a   0x0000041a   Code   RO         1452    .text               c_w.l(_printf_fp_dec.o)
    0x0800b174   0x0800b174   0x00000024   Code   RO         1454    .text               c_w.l(_printf_char_file.o)
    0x0800b198   0x0800b198   0x00000008   Code   RO         1525    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800b1a0   0x0800b1a0   0x00000008   Code   RO         1530    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800b1a8   0x0800b1a8   0x0000008a   Code   RO         1532    .text               c_w.l(lludiv10.o)
    0x0800b232   0x0800b232   0x00000002   PAD
    0x0800b234   0x0800b234   0x00000030   Code   RO         1534    .text               c_w.l(_printf_char_common.o)
    0x0800b264   0x0800b264   0x00000080   Code   RO         1536    .text               c_w.l(_printf_fp_infnan.o)
    0x0800b2e4   0x0800b2e4   0x000000dc   Code   RO         1538    .text               c_w.l(bigflt0.o)
    0x0800b3c0   0x0800b3c0   0x00000008   Code   RO         1563    .text               c_w.l(ferror.o)
    0x0800b3c8   0x0800b3c8   0x00000008   Code   RO         1576    .text               c_w.l(libspace.o)
    0x0800b3d0   0x0800b3d0   0x0000004a   Code   RO         1579    .text               c_w.l(sys_stackheap_outer.o)
    0x0800b41a   0x0800b41a   0x00000012   Code   RO         1583    .text               c_w.l(exit.o)
    0x0800b42c   0x0800b42c   0x00000080   Code   RO         1585    .text               c_w.l(strcmpv7m.o)
    0x0800b4ac   0x0800b4ac   0x0000003e   Code   RO         1541    CL$$btod_d2e        c_w.l(btod.o)
    0x0800b4ea   0x0800b4ea   0x00000046   Code   RO         1543    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800b530   0x0800b530   0x00000060   Code   RO         1542    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800b590   0x0800b590   0x00000338   Code   RO         1551    CL$$btod_div_common  c_w.l(btod.o)
    0x0800b8c8   0x0800b8c8   0x000000c6   Code   RO         1548    CL$$btod_e2e        c_w.l(btod.o)
    0x0800b98e   0x0800b98e   0x00000028   Code   RO         1545    CL$$btod_ediv       c_w.l(btod.o)
    0x0800b9b6   0x0800b9b6   0x00000028   Code   RO         1544    CL$$btod_emul       c_w.l(btod.o)
    0x0800b9de   0x0800b9de   0x00000244   Code   RO         1550    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800bc22   0x0800bc22   0x00000030   Code   RO         1490    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800bc52   0x0800bc52   0x00000006   PAD
    0x0800bc58   0x0800bc58   0x00000c50   Code   RO         1431    i.__hardfp_pow      m_wm.l(pow.o)
    0x0800c8a8   0x0800c8a8   0x000000f8   Code   RO         1492    i.__kernel_poly     m_wm.l(poly.o)
    0x0800c9a0   0x0800c9a0   0x00000030   Code   RO         1470    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x0800c9d0   0x0800c9d0   0x00000014   Code   RO         1472    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x0800c9e4   0x0800c9e4   0x00000004   PAD
    0x0800c9e8   0x0800c9e8   0x00000020   Code   RO         1473    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x0800ca08   0x0800ca08   0x00000020   Code   RO         1474    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x0800ca28   0x0800ca28   0x00000020   Code   RO         1476    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x0800ca48   0x0800ca48   0x0000000e   Code   RO         1379    i._is_digit         c_w.l(__printf_wp.o)
    0x0800ca56   0x0800ca56   0x00000018   Code   RO         1486    i.fabs              m_wm.l(fabs.o)
    0x0800ca6e   0x0800ca6e   0x0000006e   Code   RO         1497    i.sqrt              m_wm.l(sqrt.o)
    0x0800cadc   0x0800cadc   0x0000002c   Code   RO         1568    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800cb08   0x0800cb08   0x00000018   Code   RO         1457    x$fpl$basic         fz_wm.l(basic.o)
    0x0800cb20   0x0800cb20   0x00000062   Code   RO         1397    x$fpl$d2f           fz_wm.l(d2f.o)
    0x0800cb82   0x0800cb82   0x00000002   PAD
    0x0800cb84   0x0800cb84   0x00000150   Code   RO         1399    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x0800ccd4   0x0800ccd4   0x00000010   Code   RO         1570    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x0800cce4   0x0800cce4   0x00000018   Code   RO         1459    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800ccfc   0x0800ccfc   0x000002b0   Code   RO         1406    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0800cfac   0x0800cfac   0x0000005e   Code   RO         1409    x$fpl$dfix          fz_wm.l(dfix.o)
    0x0800d00a   0x0800d00a   0x00000002   PAD
    0x0800d00c   0x0800d00c   0x0000005a   Code   RO         1413    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800d066   0x0800d066   0x0000002e   Code   RO         1418    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x0800d094   0x0800d094   0x00000026   Code   RO         1417    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800d0ba   0x0800d0ba   0x00000002   PAD
    0x0800d0bc   0x0800d0bc   0x00000078   Code   RO         1423    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800d134   0x0800d134   0x00000154   Code   RO         1425    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800d288   0x0800d288   0x0000009c   Code   RO         1461    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800d324   0x0800d324   0x0000000c   Code   RO         1463    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800d330   0x0800d330   0x0000006c   Code   RO         1427    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800d39c   0x0800d39c   0x00000016   Code   RO         1400    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800d3b2   0x0800d3b2   0x00000002   PAD
    0x0800d3b4   0x0800d3b4   0x00000198   Code   RO         1572    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800d54c   0x0800d54c   0x000001d4   Code   RO         1401    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800d720   0x0800d720   0x0000000a   Code   RO         1644    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800d72a   0x0800d72a   0x0000000a   Code   RO         1465    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800d734   0x0800d734   0x00000004   Code   RO         1429    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800d738   0x0800d738   0x00000064   Code   RO         1638    x$fpl$retnan        fz_wm.l(retnan.o)
    0x0800d79c   0x0800d79c   0x0000005c   Code   RO         1467    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x0800d7f8   0x0800d7f8   0x00000030   Code   RO         1646    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0800d828   0x0800d828   0x00000000   Code   RO         1469    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800d828   0x0800d828   0x0000001c   Data   RO          660    .constdata          stm32f4xx_fsmc.o
    0x0800d844   0x0800d844   0x000017c0   Data   RO         1072    .constdata          lcd.o
    0x0800f004   0x0800f004   0x00000f8c   Data   RO         1176    .constdata          setpar.o
    0x0800ff90   0x0800ff90   0x00000ec4   Data   RO         1214    .constdata          showpar.o
    0x08010e54   0x08010e54   0x00001b12   Data   RO         1268    .constdata          showosc.o
    0x08012966   0x08012966   0x00000028   Data   RO         1352    .constdata          c_w.l(_printf_hex_int.o)
    0x0801298e   0x0801298e   0x00000002   PAD
    0x08012990   0x08012990   0x00000088   Data   RO         1434    .constdata          m_wm.l(pow.o)
    0x08012a18   0x08012a18   0x00000008   Data   RO         1494    .constdata          m_wm.l(qnan.o)
    0x08012a20   0x08012a20   0x00000094   Data   RO         1539    .constdata          c_w.l(bigflt0.o)
    0x08012ab4   0x08012ab4   0x00000020   Data   RO         1717    Region$$Table       anon$$obj.o
    0x08012ad4   0x08012ad4   0x0000001c   Data   RO         1567    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08012af0, Size: 0x00000ca8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08012af0   0x00000028   Data   RW            5    .data               main.o
    0x20000028   0x08012b18   0x00000014   Data   RW          204    .data               system_stm32f4xx.o
    0x2000003c   0x08012b2c   0x00000004   Data   RW          230    .data               delay.o
    0x20000040   0x08012b30   0x00000006   Data   RW          297    .data               usart.o
    0x20000046   0x08012b36   0x00000010   Data   RW          846    .data               stm32f4xx_rcc.o
    0x20000056   0x08012b46   0x00000004   Data   RW         1073    .data               lcd.o
    0x2000005a   0x08012b4a   0x00000006   PAD
    0x20000060   0x08012b50   0x00000018   Data   RW         1215    .data               showpar.o
    0x20000078   0x08012b68   0x0000003a   Data   RW         1269    .data               showosc.o
    0x200000b2   0x08012ba2   0x00000002   PAD
    0x200000b4        -       0x000000c8   Zero   RW          296    .bss                usart.o
    0x2000017c        -       0x0000000e   Zero   RW         1071    .bss                lcd.o
    0x2000018a        -       0x000004b0   Zero   RW         1133    .bss                dma.o
    0x2000063a        -       0x0000000a   Zero   RW         1295    .bss                tim.o
    0x20000644        -       0x00000060   Zero   RW         1577    .bss                c_w.l(libspace.o)
    0x200006a4   0x08012ba2   0x00000004   PAD
    0x200006a8        -       0x00000200   Zero   RW          325    HEAP                startup_stm32f40_41xxx.o
    0x200008a8        -       0x00000400   Zero   RW          324    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       516         20          0          0          0       2044   ad9833.o
       224         12          0          0          0        989   adc.o
       260          8          0          4          0     246961   delay.o
       112         14          0          0       1200       1155   dma.o
       104          0          0          0          0        724   exit.o
       472          4          0          0          0       1067   key.o
     19716        184       6080          4         14      23451   lcd.o
      1960        172          0         40          0      76665   main.o
       224         20          0          0          0       1873   misc.o
      1150         48          0          0          0       2562   oscuint.o
      4132        468       3980          0          0       4582   setpar.o
       420        102       6930         58          0       1386   showosc.o
      1836        222       3780         24          0       1953   showpar.o
        64         26        392          0       1536        848   startup_stm32f40_41xxx.o
      1124         24          0          0          0      10742   stm32f4xx_adc.o
       936         32          0          0          0       6453   stm32f4xx_dma.o
       272         10          0          0          0       2343   stm32f4xx_exti.o
      1480         18         28          0          0       5730   stm32f4xx_fsmc.o
       660         44          0          0          0       4217   stm32f4xx_gpio.o
        26          0          0          0          0       1250   stm32f4xx_it.o
      1628         52          0         16          0      13116   stm32f4xx_rcc.o
       148         12          0          0          0       1873   stm32f4xx_syscfg.o
      3234         60          0          0          0      23072   stm32f4xx_tim.o
      1108         34          0          0          0       7940   stm32f4xx_usart.o
       528         46          0         20          0       1863   system_stm32f4xx.o
       408         30          0          0         10       1680   tim.o
       328         16          0          6        200       3386   usart.o

    ----------------------------------------------------------------------
     43076       <USER>      <GROUP>        180       2960     449925   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          0          8          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1050          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       220          4        148          0          0         96   bigflt0.o
      1910        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       164         44          0          0          0        620   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     11868        <USER>        <GROUP>          0        100       7720   Library Totals
        24          0          2          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4746        212        216          0         96       2864   c_w.l
      3352        248          0          0          0       3336   fz_wm.l
      3746        340        144          0          0       1520   m_wm.l

    ----------------------------------------------------------------------
     11868        <USER>        <GROUP>          0        100       7720   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     54944       2478      21584        180       3060     447585   Grand Totals
     54944       2478      21584        180       3060     447585   ELF Image Totals
     54944       2478      21584        180          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                76528 (  74.73kB)
    Total RW  Size (RW Data + ZI Data)              3240 (   3.16kB)
    Total ROM Size (Code + RO Data + RW Data)      76708 (  74.91kB)

==============================================================================

