/**
 * @file    optimized_example.c
 * @brief   基于商家代码优化的STM32F407驱动使用示例
 * <AUTHOR>
 * @date    2024
 * @note    展示优化后的DAC8552、AD7606、CD4052驱动使用方法
 */

#include "bsp.h"
#include "dac8552.h"
#include "ad7606.h"
#include "cd4052.h"
#include "systick.h"
#include "usart.h"

// 测试数据缓冲区
static int16_t adc_data[AD7606_CHANNEL_COUNT];

/**
 * @brief  系统初始化
 * @param  None
 * @retval 0: 成功, 1: 失败
 */
uint8_t System_Init(void)
{
    uint8_t result;
    
    // 1. BSP初始化
    BSP_Init();
    
    // 2. 串口初始化 (用于调试输出)
    USART_Init();
    USART_Printf("=== 电赛仪器仪表系统启动 ===\r\n");
    
    // 3. DAC8552初始化
    result = DAC8552_Init();
    if (result != DAC8552_OK)
    {
        USART_Printf("DAC8552初始化失败: %d\r\n", result);
        return 1;
    }
    USART_Printf("DAC8552初始化成功\r\n");
    
    // 4. CD4052初始化
    result = CD4052_Init();
    if (result != CD4052_OK)
    {
        USART_Printf("CD4052初始化失败: %d\r\n", result);
        return 1;
    }
    USART_Printf("CD4052初始化成功\r\n");
    
    // 5. AD7606初始化
    result = AD7606_Init();
    if (result != AD7606_OK)
    {
        USART_Printf("AD7606初始化失败: %d\r\n", result);
        return 1;
    }
    USART_Printf("AD7606初始化成功\r\n");
    
    USART_Printf("系统初始化完成\r\n\r\n");
    return 0;
}

/**
 * @brief  DAC输出测试
 * @param  None
 * @retval None
 */
void DAC_Test(void)
{
    uint8_t result;
    float voltage;
    
    USART_Printf("=== DAC8552输出测试 ===\r\n");
    
    // 测试不同电压输出
    for (voltage = 0.0f; voltage <= 5.0f; voltage += 1.0f)
    {
        // 通道A输出
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, voltage);
        if (result == DAC8552_OK)
        {
            USART_Printf("通道A输出: %.1fV - 成功\r\n", voltage);
        }
        else
        {
            USART_Printf("通道A输出: %.1fV - 失败(%d)\r\n", voltage, result);
        }
        
        // 通道B输出
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_B, voltage + 0.5f);
        if (result == DAC8552_OK)
        {
            USART_Printf("通道B输出: %.1fV - 成功\r\n", voltage + 0.5f);
        }
        else
        {
            USART_Printf("通道B输出: %.1fV - 失败(%d)\r\n", voltage + 0.5f, result);
        }
        
        SysTick_Delay_ms(500);  // 延时500ms观察输出
    }
    
    USART_Printf("DAC测试完成\r\n\r\n");
}

/**
 * @brief  增益控制测试
 * @param  None
 * @retval None
 */
void Gain_Test(void)
{
    uint8_t result;
    uint8_t level;
    
    USART_Printf("=== CD4052增益控制测试 ===\r\n");
    
    // 测试所有增益级别
    for (level = CD4052_GAIN_LEVEL_0; level <= CD4052_GAIN_LEVEL_3; level++)
    {
        result = CD4052_SetGain(level);
        if (result == CD4052_OK)
        {
            USART_Printf("设置增益级别%d - 成功\r\n", level);
            USART_Printf("当前增益级别: %d\r\n", CD4052_GetGain());
        }
        else
        {
            USART_Printf("设置增益级别%d - 失败(%d)\r\n", level, result);
        }
        
        SysTick_Delay_ms(200);
    }
    
    USART_Printf("增益控制测试完成\r\n\r\n");
}

/**
 * @brief  ADC采集测试
 * @param  None
 * @retval None
 */
void ADC_Test(void)
{
    uint8_t result;
    uint8_t i;
    
    USART_Printf("=== AD7606采集测试 ===\r\n");
    
    // 进行10次采集测试
    for (uint8_t test_count = 0; test_count < 10; test_count++)
    {
        result = AD7606_ReadData(adc_data);
        if (result == AD7606_OK)
        {
            USART_Printf("采集%d - 成功: ", test_count + 1);
            for (i = 0; i < AD7606_CHANNEL_COUNT; i++)
            {
                USART_Printf("CH%d:%d ", i, adc_data[i]);
            }
            USART_Printf("\r\n");
        }
        else
        {
            USART_Printf("采集%d - 失败(%d)\r\n", test_count + 1, result);
        }
        
        SysTick_Delay_ms(100);
    }
    
    USART_Printf("ADC采集测试完成\r\n\r\n");
}

/**
 * @brief  综合测试 - 模拟实际电赛应用
 * @param  None
 * @retval None
 */
void Comprehensive_Test(void)
{
    uint8_t result;
    uint8_t gain_level = CD4052_GAIN_LEVEL_0;
    float dac_voltage = 1.0f;
    
    USART_Printf("=== 综合测试 - 模拟电赛应用 ===\r\n");
    
    for (uint8_t cycle = 0; cycle < 5; cycle++)
    {
        USART_Printf("--- 测试周期 %d ---\r\n", cycle + 1);
        
        // 1. 设置DAC输出
        result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, dac_voltage);
        if (result == DAC8552_OK)
        {
            USART_Printf("DAC输出设置: %.1fV\r\n", dac_voltage);
        }
        
        // 2. 设置增益级别
        result = CD4052_SetGain(gain_level);
        if (result == CD4052_OK)
        {
            USART_Printf("增益级别设置: %d\r\n", gain_level);
        }
        
        // 3. 等待系统稳定
        SysTick_Delay_ms(50);
        
        // 4. 进行ADC采集
        result = AD7606_ReadData(adc_data);
        if (result == AD7606_OK)
        {
            USART_Printf("ADC采集结果: ");
            for (uint8_t i = 0; i < 4; i++)  // 只显示前4个通道
            {
                USART_Printf("CH%d:%d ", i, adc_data[i]);
            }
            USART_Printf("\r\n");
        }
        
        // 5. 更新参数
        dac_voltage += 0.5f;
        if (dac_voltage > 5.0f) dac_voltage = 1.0f;
        
        gain_level = (gain_level + 1) % 4;
        
        SysTick_Delay_ms(500);
    }
    
    USART_Printf("综合测试完成\r\n\r\n");
}

/**
 * @brief  主函数
 * @param  None
 * @retval None
 */
int main(void)
{
    // 系统初始化
    if (System_Init() != 0)
    {
        USART_Printf("系统初始化失败，程序停止\r\n");
        while (1);
    }
    
    // 运行测试
    while (1)
    {
        DAC_Test();
        Gain_Test();
        ADC_Test();
        Comprehensive_Test();
        
        USART_Printf("=== 所有测试完成，5秒后重新开始 ===\r\n\r\n");
        SysTick_Delay_ms(5000);
    }
}
