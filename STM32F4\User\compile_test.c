/**
 * @file    compile_test.c
 * @brief   编译测试文件 - 验证所有驱动模块的编译兼容性
 * <AUTHOR>
 * @date    2024
 * @note    用于快速验证编译配置是否正确
 */

#include "stm32f4xx.h"
#include "bsp.h"
#include "dac8552.h"
#include "ad7606.h"
#include "cd4052.h"
#include "systick.h"
#include "usart.h"

/**
 * @brief  编译测试函数
 * @param  None
 * @retval 0: 编译通过
 * @note   此函数仅用于编译测试，不会被实际调用
 */
uint8_t Compile_Test(void)
{
    uint8_t result = 0;
    int16_t adc_data[AD7606_CHANNEL_COUNT];
    
    // 测试BSP函数
    BSP_Init();
    
    // 测试DAC8552函数
    result = DAC8552_Init();
    result = DAC8552_SetVoltage(DAC8552_CHANNEL_A, 2.5f);
    result = DAC8552_Write(DAC8552_CHANNEL_B, 0x8000);
    result = DAC8552_Test();
    
    // 测试CD4052函数
    result = CD4052_Init();
    result = CD4052_SetGain(CD4052_GAIN_LEVEL_1);
    uint8_t gain = CD4052_GetGain();
    
    // 测试AD7606函数
    result = AD7606_Init();
    result = AD7606_StartConversion();
    result = AD7606_ReadData(adc_data);
    result = AD7606_Reset();
    uint8_t busy = AD7606_IsBusy();
    result = AD7606_Test();
    
    // 测试SysTick函数
    uint32_t tick = SysTick_GetTick();
    Delay_ms(1);
    Delay_us(100);
    
    // 避免未使用变量警告
    (void)gain;
    (void)busy;
    (void)tick;
    
    return result;
}

/**
 * @brief  函数指针测试
 * @param  None
 * @retval None
 * @note   验证所有函数声明正确
 */
void Function_Pointer_Test(void)
{
    // DAC8552函数指针
    uint8_t (*dac_init_ptr)(void) = DAC8552_Init;
    uint8_t (*dac_set_voltage_ptr)(uint8_t, float) = DAC8552_SetVoltage;
    uint8_t (*dac_write_ptr)(uint8_t, uint16_t) = DAC8552_Write;
    uint8_t (*dac_test_ptr)(void) = DAC8552_Test;
    
    // CD4052函数指针
    uint8_t (*cd4052_init_ptr)(void) = CD4052_Init;
    uint8_t (*cd4052_set_gain_ptr)(uint8_t) = CD4052_SetGain;
    uint8_t (*cd4052_get_gain_ptr)(void) = CD4052_GetGain;
    
    // AD7606函数指针
    uint8_t (*ad7606_init_ptr)(void) = AD7606_Init;
    uint8_t (*ad7606_start_conv_ptr)(void) = AD7606_StartConversion;
    uint8_t (*ad7606_read_data_ptr)(int16_t*) = AD7606_ReadData;
    uint8_t (*ad7606_reset_ptr)(void) = AD7606_Reset;
    uint8_t (*ad7606_is_busy_ptr)(void) = AD7606_IsBusy;
    uint8_t (*ad7606_test_ptr)(void) = AD7606_Test;
    
    // BSP函数指针
    void (*bsp_init_ptr)(void) = BSP_Init;
    
    // 避免未使用变量警告
    (void)dac_init_ptr;
    (void)dac_set_voltage_ptr;
    (void)dac_write_ptr;
    (void)dac_test_ptr;
    (void)cd4052_init_ptr;
    (void)cd4052_set_gain_ptr;
    (void)cd4052_get_gain_ptr;
    (void)ad7606_init_ptr;
    (void)ad7606_start_conv_ptr;
    (void)ad7606_read_data_ptr;
    (void)ad7606_reset_ptr;
    (void)ad7606_is_busy_ptr;
    (void)ad7606_test_ptr;
    (void)bsp_init_ptr;
}

/**
 * @brief  常量定义测试
 * @param  None
 * @retval None
 * @note   验证所有常量定义正确
 */
void Constants_Test(void)
{
    // DAC8552常量
    uint8_t dac_channels[] = {DAC8552_CHANNEL_A, DAC8552_CHANNEL_B};
    uint8_t dac_cmds[] = {DAC8552_CMD_WRITE_A, DAC8552_CMD_WRITE_B};
    uint16_t dac_max = DAC8552_MAX_VALUE;
    uint8_t dac_errors[] = {DAC8552_OK, DAC8552_ERROR, DAC8552_TIMEOUT, DAC8552_PARAM_ERROR};
    
    // CD4052常量
    uint8_t cd4052_levels[] = {CD4052_GAIN_LEVEL_0, CD4052_GAIN_LEVEL_1, 
                               CD4052_GAIN_LEVEL_2, CD4052_GAIN_LEVEL_3};
    uint8_t cd4052_errors[] = {CD4052_OK, CD4052_PARAM_ERROR};
    
    // AD7606常量
    uint8_t ad7606_channels = AD7606_CHANNEL_COUNT;
    int16_t ad7606_range[] = {AD7606_MIN_VALUE, AD7606_MAX_VALUE};
    uint8_t ad7606_errors[] = {AD7606_OK, AD7606_ERROR, AD7606_TIMEOUT, 
                               AD7606_PARAM_ERROR, AD7606_BUSY_ERROR};
    
    // 避免未使用变量警告
    (void)dac_channels;
    (void)dac_cmds;
    (void)dac_max;
    (void)dac_errors;
    (void)cd4052_levels;
    (void)cd4052_errors;
    (void)ad7606_channels;
    (void)ad7606_range;
    (void)ad7606_errors;
}

/**
 * @brief  编译测试主函数
 * @param  None
 * @retval 0: 所有测试通过
 * @note   如果此函数能够编译通过，说明所有驱动模块配置正确
 */
int Compile_Test_Main(void)
{
    // 执行各项编译测试
    uint8_t result = Compile_Test();
    Function_Pointer_Test();
    Constants_Test();
    
    // 如果能执行到这里，说明编译测试通过
    return (int)result;
}
