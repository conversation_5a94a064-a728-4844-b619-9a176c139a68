Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to dds_wavegen.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to adc_dma.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to dds_wavegen.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to adc_dma.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.G_Comprehensive_Test) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.G_Comprehensive_Test) refers to dac8552.o(i.DAC8552_SetVoltage) for DAC8552_SetVoltage
    main.o(i.G_Comprehensive_Test) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.G_Comprehensive_Test) refers to cd4052.o(i.CD4052_SetGain) for CD4052_SetGain
    main.o(i.G_Comprehensive_Test) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.G_Comprehensive_Test) refers to ad7606.o(i.AD7606_ReadData) for AD7606_ReadData
    main.o(i.G_Comprehensive_Test) refers to main.o(.bss) for g_adc7606_data
    main.o(i.G_Module_Init) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.G_Module_Init) refers to bsp.o(i.BSP_Init) for BSP_Init
    main.o(i.G_Module_Init) refers to dac8552.o(i.DAC8552_Init) for DAC8552_Init
    main.o(i.G_Module_Init) refers to cd4052.o(i.CD4052_Init) for CD4052_Init
    main.o(i.G_Module_Init) refers to ad7606.o(i.AD7606_Init) for AD7606_Init
    main.o(i.G_Module_Test) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.G_Module_Test) refers to dac8552.o(i.DAC8552_SetVoltage) for DAC8552_SetVoltage
    main.o(i.G_Module_Test) refers to cd4052.o(i.CD4052_SetGain) for CD4052_SetGain
    main.o(i.G_Module_Test) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.G_Module_Test) refers to ad7606.o(i.AD7606_ReadData) for AD7606_ReadData
    main.o(i.G_Module_Test) refers to main.o(.bss) for g_adc7606_data
    main.o(i.Module_Init_Demo) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.Module_Init_Demo) refers to usart.o(i.USART1_Init) for USART1_Init
    main.o(i.Module_Init_Demo) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.Module_Init_Demo) refers to adc_dma.o(i.ADC1_DMA_Init) for ADC1_DMA_Init
    main.o(i.Module_Init_Demo) refers to adc_dma.o(i.ADC_Start_Acquisition) for ADC_Start_Acquisition
    main.o(i.Module_Init_Demo) refers to parallel_adc.o(i.ParallelADC_Init) for ParallelADC_Init
    main.o(i.Module_Init_Demo) refers to parallel_adc.o(i.ParallelADC_Start) for ParallelADC_Start
    main.o(i.Module_Init_Demo) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.Module_Init_Demo) refers to dds_wavegen.o(i.DDS_Init) for DDS_Init
    main.o(i.Module_Init_Demo) refers to dds_wavegen.o(i.DDS_Start) for DDS_Start
    main.o(i.Module_Init_Demo) refers to oled_1.o(i.OLED_Init) for OLED_Init
    main.o(i.Module_Init_Demo) refers to oled_1.o(i.OLED_Clear) for OLED_Clear
    main.o(i.Module_Init_Demo) refers to oled_1.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Module_Init_Demo) refers to oled_1.o(i.OLED_Refresh) for OLED_Refresh
    main.o(i.Module_Init_Demo) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.Module_Init_Demo) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    main.o(i.Module_Init_Demo) refers to main.o(.constdata) for .constdata
    main.o(i.Module_Test_Demo) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.Module_Test_Demo) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    main.o(i.Module_Test_Demo) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.Module_Test_Demo) refers to dds_wavegen.o(i.DDS_SetFrequency) for DDS_SetFrequency
    main.o(i.Module_Test_Demo) refers to dds_wavegen.o(i.DDS_SetWaveType) for DDS_SetWaveType
    main.o(i.Module_Test_Demo) refers to adc_dma.o(i.ADC_Calibrate) for ADC_Calibrate
    main.o(i.assert_failed) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to main.o(i.Module_Init_Demo) for Module_Init_Demo
    main.o(i.main) refers to main.o(i.G_Module_Init) for G_Module_Init
    main.o(i.main) refers to usart.o(i.USART_Printf) for USART_Printf
    main.o(i.main) refers to main.o(i.Module_Test_Demo) for Module_Test_Demo
    main.o(i.main) refers to main.o(i.G_Module_Test) for G_Module_Test
    main.o(i.main) refers to adc_dma.o(i.ADC_ProcessData) for ADC_ProcessData
    main.o(i.main) refers to adc_dma.o(i.ADC_GetStats) for ADC_GetStats
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to usart.o(i.USART_SendString) for USART_SendString
    main.o(i.main) refers to ad7606.o(i.AD7606_ReadData) for AD7606_ReadData
    main.o(i.main) refers to parallel_adc.o(i.ParallelADC_ReadSingle) for ParallelADC_ReadSingle
    main.o(i.main) refers to dds_wavegen.o(i.DDS_GetStats) for DDS_GetStats
    main.o(i.main) refers to key.o(i.Key_Scan) for Key_Scan
    main.o(i.main) refers to cd4052.o(i.CD4052_SetGain) for CD4052_SetGain
    main.o(i.main) refers to oled_1.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled_1.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled_1.o(i.OLED_Refresh) for OLED_Refresh
    main.o(i.main) refers to dac8552.o(i.DAC8552_SetVoltage) for DAC8552_SetVoltage
    main.o(i.main) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    main.o(i.main) refers to systick.o(i.SysTick_GetUptime_ms) for SysTick_GetUptime_ms
    main.o(i.main) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to adc_dma.o(.data) for g_adc_dma_full_complete
    main.o(i.main) refers to main.o(.bss) for debug_buffer
    main.o(i.main) refers to main.o(.data) for ad7606_counter
    main.o(i.main) refers to parallel_adc.o(.data) for g_parallel_adc_data_ready
    main.o(i.main) refers to dds_wavegen.o(.data) for g_dds_update_complete
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to parallel_adc.o(i.EXTI0_IRQHandler_Internal) for EXTI0_IRQHandler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.SysTick_Handler_Internal) for SysTick_Handler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_CommonInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ContinuousModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_RegularChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SetInjectedOffset) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_VBATCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_CancelTransmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DBGFreeze) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_FIFORelease) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_FilterInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetLastErrorCode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_MessagePending) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_OperatingModeRequest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Receive) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_SlaveStartBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Sleep) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TTComModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Transmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TransmitStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_WakeUp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_PhaseConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetDataOutputValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel1Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel2Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetDualChannelData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_WaveGenerationCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CROPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CaptureCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_JPEGCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_FlowControllerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_MemoryTargetConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Suspend) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_PCCARDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_AutoStartDigest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_ARPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CalculatePEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CheckEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DualAddressCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GeneralCallCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTART) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTOP) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetLastEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_i2c.o(i.I2C_NACKPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_OwnAddress2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_PECPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReadRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Send7bitAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_StretchClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_TransmitPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetReload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_ltdc.o(i.LTDC_DitherCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetCDStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LIPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ReloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_EnterSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDLevelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_UnderDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rng.o(i.RNG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_BypassShadowCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_OutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_OutputTypeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ReadBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_TamperCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_CompandingModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sai.o(i.SAI_FlushFIFO) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_FrameInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MonoModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteValueConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SlotInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_TRIStateConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CEATAITCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DataConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetResponse) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCEATACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCommand) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetPowerState) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_FullDuplexConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_CalculateCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_DataSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRCPolynomial) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_SSOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TIModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TransmitCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_BDTRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCPreloadControl) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxNCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC1Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC2Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC3Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC4Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CounterModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC3Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC4Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GenerateEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_InternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PrescalerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_RemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCCDMA) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCOM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectHallSensor) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectInputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOCxM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOnePulseMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetAutoreload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetClockDivision) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TimeBaseInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateRequestConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClockInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_HalfDuplexCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_usart.o(i.USART_IrDACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_IrDAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OneBitMethodCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OverSampling8Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendBreak) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetGuardTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardNACKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_WakeUpConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_wwdg.o(i.WWDG_Enable) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetWindowValue) refers to main.o(i.assert_failed) for assert_failed
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_GPIO_Config) for ADC_GPIO_Config
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_DMA_Config) for ADC_DMA_Config
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_NVIC_Config) for ADC_NVIC_Config
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_Config) for ADC_Config
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_Timer_Config) for ADC_Timer_Config
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(i.ADC_ResetStats) for ADC_ResetStats
    adc_dma.o(i.ADC1_DMA_Init) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_ApplyCalibration) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_Calibrate) refers to systick.o(i.Delay_ms) for Delay_ms
    adc_dma.o(i.ADC_Calibrate) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc_dma.o(i.ADC_Config) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) for DMA_DoubleBufferModeConfig
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) for DMA_DoubleBufferModeCmd
    adc_dma.o(i.ADC_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    adc_dma.o(i.ADC_DMA_Config) refers to adc_dma.o(.bss) for s_adc_ping_buffer
    adc_dma.o(i.ADC_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc_dma.o(i.ADC_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc_dma.o(i.ADC_GetChannelValue) refers to adc_dma.o(i.ADC_ApplyCalibration) for ADC_ApplyCalibration
    adc_dma.o(i.ADC_GetChannelValue) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_GetChannelVoltage_mV) refers to adc_dma.o(i.ADC_GetChannelValue) for ADC_GetChannelValue
    adc_dma.o(i.ADC_GetMultiChannelData) refers to adc_dma.o(i.ADC_ApplyCalibration) for ADC_ApplyCalibration
    adc_dma.o(i.ADC_GetMultiChannelData) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    adc_dma.o(i.ADC_GetStats) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    adc_dma.o(i.ADC_GetStats) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_GetStats) refers to adc_dma.o(.data) for last_time
    adc_dma.o(i.ADC_IRQHandler) refers to stm32f4xx_adc.o(i.ADC_GetITStatus) for ADC_GetITStatus
    adc_dma.o(i.ADC_IRQHandler) refers to stm32f4xx_adc.o(i.ADC_ClearITPendingBit) for ADC_ClearITPendingBit
    adc_dma.o(i.ADC_IRQHandler) refers to adc_dma.o(.data) for g_adc_conversion_complete
    adc_dma.o(i.ADC_IRQHandler) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    adc_dma.o(i.ADC_ProcessData) refers to adc_dma.o(i.ADC_UpdateStats) for ADC_UpdateStats
    adc_dma.o(i.ADC_ProcessData) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc_dma.o(i.ADC_ResetStats) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_SetSampleRate) refers to adc_dma.o(i.ADC_Timer_Config) for ADC_Timer_Config
    adc_dma.o(i.ADC_SetSampleRate) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_Start_Acquisition) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc_dma.o(i.ADC_Start_Acquisition) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    adc_dma.o(i.ADC_Start_Acquisition) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_Start_Acquisition) refers to adc_dma.o(.data) for g_adc_conversion_complete
    adc_dma.o(i.ADC_Stop_Acquisition) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    adc_dma.o(i.ADC_Stop_Acquisition) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc_dma.o(i.ADC_Stop_Acquisition) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.ADC_Timer_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    adc_dma.o(i.ADC_Timer_Config) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    adc_dma.o(i.ADC_Timer_Config) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    adc_dma.o(i.ADC_Timer_Config) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc_dma.o(i.ADC_UpdateStats) refers to adc_dma.o(.bss) for g_adc1_handle
    adc_dma.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    adc_dma.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc_dma.o(i.DMA2_Stream0_IRQHandler) refers to adc_dma.o(.data) for g_adc_dma_half_complete
    adc_dma.o(i.DMA2_Stream0_IRQHandler) refers to adc_dma.o(.bss) for g_adc1_handle
    parallel_adc.o(i.EXTI0_IRQHandler_Internal) refers to stm32f4xx_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    parallel_adc.o(i.EXTI0_IRQHandler_Internal) refers to stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    parallel_adc.o(i.EXTI0_IRQHandler_Internal) refers to parallel_adc.o(.data) for g_adc_buffer_index
    parallel_adc.o(i.EXTI0_IRQHandler_Internal) refers to parallel_adc.o(.bss) for g_adc_buffer
    parallel_adc.o(i.ParallelADC_Buffer_Init) refers to parallel_adc.o(.bss) for s_parallel_adc_buffer
    parallel_adc.o(i.ParallelADC_Buffer_Read) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_Buffer_Write) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_ClearBuffer) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_EXTI_Config) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    parallel_adc.o(i.ParallelADC_EXTI_Config) refers to stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) for SYSCFG_EXTILineConfig
    parallel_adc.o(i.ParallelADC_EXTI_Config) refers to stm32f4xx_exti.o(i.EXTI_Init) for EXTI_Init
    parallel_adc.o(i.ParallelADC_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    parallel_adc.o(i.ParallelADC_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    parallel_adc.o(i.ParallelADC_GPIO_Config) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_GetSampleRate) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_GetState) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    parallel_adc.o(i.ParallelADC_GetStats) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(i.ParallelADC_Buffer_Init) for ParallelADC_Buffer_Init
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(i.ParallelADC_GPIO_Config) for ParallelADC_GPIO_Config
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(i.ParallelADC_EXTI_Config) for ParallelADC_EXTI_Config
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(i.ParallelADC_NVIC_Config) for ParallelADC_NVIC_Config
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(i.ParallelADC_ResetStats) for ParallelADC_ResetStats
    parallel_adc.o(i.ParallelADC_Init) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    parallel_adc.o(i.ParallelADC_ReadMultiple) refers to parallel_adc.o(i.ParallelADC_Buffer_Read) for ParallelADC_Buffer_Read
    parallel_adc.o(i.ParallelADC_ReadSingle) refers to parallel_adc.o(i.ParallelADC_Buffer_Read) for ParallelADC_Buffer_Read
    parallel_adc.o(i.ParallelADC_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    parallel_adc.o(i.ParallelADC_ResetStats) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    parallel_adc.o(i.ParallelADC_ResetStats) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_ResetStats) refers to parallel_adc.o(.data) for s_sample_count
    parallel_adc.o(i.ParallelADC_SetTriggerEdge) refers to parallel_adc.o(i.ParallelADC_EXTI_Config) for ParallelADC_EXTI_Config
    parallel_adc.o(i.ParallelADC_SetTriggerEdge) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_Start) refers to parallel_adc.o(i.ParallelADC_ClearBuffer) for ParallelADC_ClearBuffer
    parallel_adc.o(i.ParallelADC_Start) refers to stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    parallel_adc.o(i.ParallelADC_Start) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_Start) refers to parallel_adc.o(.data) for g_parallel_adc_data_ready
    parallel_adc.o(i.ParallelADC_Stop) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_UpdateStats) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    parallel_adc.o(i.ParallelADC_UpdateStats) refers to parallel_adc.o(.bss) for g_parallel_adc_handle
    parallel_adc.o(i.ParallelADC_UpdateStats) refers to parallel_adc.o(.data) for s_last_time
    systick.o(i.DWT_Init) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.Delay_ms) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_ms) refers to systick.o(.data) for s_delay_counter
    systick.o(i.Delay_s) refers to systick.o(i.Delay_ms) for Delay_ms
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_GetCalibratedDelay) for SysTick_GetCalibratedDelay
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_us) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.SysTick_Calibrate) refers to systick.o(i.Delay_us) for Delay_us
    systick.o(i.SysTick_Calibrate) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetCalibratedDelay) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_GetTick) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_GetTimestamp_us) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_GetUptime_ms) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_Handler_Internal) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_Init) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.SysTick_Init) refers to systick.o(i.DWT_Init) for DWT_Init
    systick.o(i.SysTick_Init) refers to systick.o(i.SysTick_ResetStats) for SysTick_ResetStats
    systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    systick.o(i.SysTick_Init) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_ResetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_SetTemperatureCompensation) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.data) for g_system_uptime_ms
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.data) for g_usart_rx_complete_flag
    usart.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    usart.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    usart.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) for DMA_GetCurrDataCounter
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for g_usart_rx_complete_flag
    usart.o(i.USART1_Init) refers to usart.o(i.RingBuffer_Init) for RingBuffer_Init
    usart.o(i.USART1_Init) refers to usart.o(i.USART_GPIO_Config) for USART_GPIO_Config
    usart.o(i.USART1_Init) refers to usart.o(i.USART_DMA_Config) for USART_DMA_Config
    usart.o(i.USART1_Init) refers to usart.o(i.USART_NVIC_Config) for USART_NVIC_Config
    usart.o(i.USART1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART1_Init) refers to usart.o(i.USART_ResetStats) for USART_ResetStats
    usart.o(i.USART1_Init) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    usart.o(i.USART_DMA_Config) refers to usart.o(.bss) for s_tx_buffer
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    usart.o(i.USART_GetStats) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_IsTxComplete) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART_Module_ReceiveData) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_Module_ReceiveData) refers to usart.o(i.USART_ReceiveByte) for USART_ReceiveByte
    usart.o(i.USART_Module_SendData) refers to usart.o(i.USART_SendByte) for USART_SendByte
    usart.o(i.USART_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.USART_Printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(i.USART_Printf) refers to usart.o(i.USART_SendString) for USART_SendString
    usart.o(i.USART_Printf) refers to usart.o(.bss) for s_printf_buffer
    usart.o(i.USART_ReceiveByte) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_ReceiveByte) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_ReceiveByte) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART_ReceiveByte) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.USART_ResetStats) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendByte) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_SendByte) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART_SendByte) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendData_DMA) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    usart.o(i.USART_SendData_DMA) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART_SendData_DMA) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.USART_SendData_DMA) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendData_DMA) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART_SendHex) refers to usart.o(i.USART_Printf) for USART_Printf
    usart.o(i.USART_SendString) refers to strlen.o(.text) for strlen
    usart.o(i.USART_SendString) refers to usart.o(i.USART_Module_SendData) for USART_Module_SendData
    usart.o(i.USART_WaitTxComplete) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_WaitTxComplete) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.fgetc) refers to usart.o(i.USART_ReceiveByte) for USART_ReceiveByte
    usart.o(i.fputc) refers to usart.o(i.USART_SendByte) for USART_SendByte
    dds_wavegen.o(i.DDS_CalculateTHD) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_ConfigModulation) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_wavegen.o(i.DDS_ConfigModulation) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_DAC_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    dds_wavegen.o(i.DDS_DAC_Config) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    dds_wavegen.o(i.DDS_DAC_Config) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    dds_wavegen.o(i.DDS_DAC_Config) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    dds_wavegen.o(i.DDS_DMA_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    dds_wavegen.o(i.DDS_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    dds_wavegen.o(i.DDS_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    dds_wavegen.o(i.DDS_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    dds_wavegen.o(i.DDS_DMA_Config) refers to dds_wavegen.o(.bss) for s_dds_output_buffer
    dds_wavegen.o(i.DDS_EnableModulation) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    dds_wavegen.o(i.DDS_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dds_wavegen.o(i.DDS_GenerateWaveTable) refers to dds_wavegen.o(.constdata) for g_sin_wave_table
    dds_wavegen.o(i.DDS_GenerateWaveTable) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_GetCurrentOutput) refers to stm32f4xx_dac.o(i.DAC_GetDataOutputValue) for DAC_GetDataOutputValue
    dds_wavegen.o(i.DDS_GetNextSample) refers to dds_wavegen.o(i.DDS_ProcessModulation) for DDS_ProcessModulation
    dds_wavegen.o(i.DDS_GetNextSample) refers to dds_wavegen.o(i.DDS_LinearInterpolation) for DDS_LinearInterpolation
    dds_wavegen.o(i.DDS_GetNextSample) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    dds_wavegen.o(i.DDS_GetStats) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_Init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_GPIO_Config) for DDS_GPIO_Config
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_DAC_Config) for DDS_DAC_Config
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_TIM_Config) for DDS_TIM_Config
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_DMA_Config) for DDS_DMA_Config
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_NVIC_Config) for DDS_NVIC_Config
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_GenerateWaveTable) for DDS_GenerateWaveTable
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_UpdateFrequencyWord) for DDS_UpdateFrequencyWord
    dds_wavegen.o(i.DDS_Init) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_GetNextSample) for DDS_GetNextSample
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(i.DDS_ResetStats) for DDS_ResetStats
    dds_wavegen.o(i.DDS_Init) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_LinearInterpolation) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    dds_wavegen.o(i.DDS_ProcessModulation) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_wavegen.o(i.DDS_ProcessModulation) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_ProcessModulation) refers to dds_wavegen.o(.constdata) for g_sin_wave_table
    dds_wavegen.o(i.DDS_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dds_wavegen.o(i.DDS_ResetStats) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetAmplitude) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetCustomWave) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetFrequency) refers to dds_wavegen.o(i.DDS_UpdateFrequencyWord) for DDS_UpdateFrequencyWord
    dds_wavegen.o(i.DDS_SetFrequency) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetOffset) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetPhase) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_wavegen.o(i.DDS_SetPhase) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_SetWaveType) refers to dds_wavegen.o(i.DDS_GenerateWaveTable) for DDS_GenerateWaveTable
    dds_wavegen.o(i.DDS_SetWaveType) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_Start) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    dds_wavegen.o(i.DDS_Start) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dds_wavegen.o(i.DDS_Start) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_Start) refers to dds_wavegen.o(.data) for g_dds_update_complete
    dds_wavegen.o(i.DDS_Stop) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dds_wavegen.o(i.DDS_Stop) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    dds_wavegen.o(i.DDS_Stop) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    dds_wavegen.o(i.DDS_Stop) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DDS_TIM_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    dds_wavegen.o(i.DDS_TIM_Config) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    dds_wavegen.o(i.DDS_TIM_Config) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    dds_wavegen.o(i.DDS_TIM_Config) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dds_wavegen.o(i.DDS_UpdateFrequencyWord) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_wavegen.o(i.DDS_UpdateFrequencyWord) refers to dds_wavegen.o(.bss) for g_dds_handle
    dds_wavegen.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    dds_wavegen.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    dds_wavegen.o(i.DMA1_Stream5_IRQHandler) refers to dds_wavegen.o(i.DDS_GetNextSample) for DDS_GetNextSample
    dds_wavegen.o(i.DMA1_Stream5_IRQHandler) refers to dds_wavegen.o(.bss) for s_dds_output_buffer
    dds_wavegen.o(i.DMA1_Stream5_IRQHandler) refers to dds_wavegen.o(.data) for g_dds_dma_complete
    dds_wavegen.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    dds_wavegen.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    dds_wavegen.o(i.TIM6_DAC_IRQHandler) refers to dds_wavegen.o(.data) for g_dds_update_complete
    key.o(i.Key_DeInit) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.Key_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_GetState) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_Init) refers to key.o(i.Key_GPIO_Config) for Key_GPIO_Config
    key.o(i.Key_Init) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_Init) refers to key.o(.data) for s_key_states
    key.o(i.Key_Init) refers to key.o(.bss) for s_key_debounce_time
    key.o(i.Key_IsPressed) refers to key.o(i.Key_GetState) for Key_GetState
    key.o(i.Key_IsReady) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_ReadRaw) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Scan) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_Scan) refers to key.o(i.Key_ReadRaw) for Key_ReadRaw
    key.o(i.Key_Scan) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_Scan) refers to key.o(.bss) for s_key_debounce_time
    key.o(i.Key_WaitPress) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_WaitPress) refers to key.o(i.Key_Scan) for Key_Scan
    key.o(i.Key_WaitPress) refers to systick.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_WaitPress) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_WaitRelease) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_WaitRelease) refers to key.o(i.Key_Scan) for Key_Scan
    key.o(i.Key_WaitRelease) refers to systick.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_WaitRelease) refers to key.o(i.Key_IsPressed) for Key_IsPressed
    key.o(i.Key_WaitRelease) refers to key.o(.data) for g_key_initialized
    oled_1.o(i.OLED_Clear) refers to rt_memclr.o(.text) for __aeabi_memclr
    oled_1.o(i.OLED_Clear) refers to oled_1.o(i.OLED_Refresh) for OLED_Refresh
    oled_1.o(i.OLED_Clear) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_Clear) refers to oled_1.o(.bss) for s_oled_buffer
    oled_1.o(i.OLED_DrawLine) refers to oled_1.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled_1.o(i.OLED_DrawLine) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_DrawPoint) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_DrawPoint) refers to oled_1.o(.bss) for s_oled_buffer
    oled_1.o(i.OLED_DrawRect) refers to oled_1.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled_1.o(i.OLED_DrawRect) refers to oled_1.o(i.OLED_DrawLine) for OLED_DrawLine
    oled_1.o(i.OLED_DrawRect) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_i2c.o(i.I2C_DeInit) for I2C_DeInit
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_i2c.o(i.I2C_Init) for I2C_Init
    oled_1.o(i.OLED_I2C_Config) refers to stm32f4xx_i2c.o(i.I2C_Cmd) for I2C_Cmd
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_GetFlagStatus) for I2C_GetFlagStatus
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTART) for I2C_GenerateSTART
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_CheckEvent) for I2C_CheckEvent
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_Send7bitAddress) for I2C_Send7bitAddress
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_SendData) for I2C_SendData
    oled_1.o(i.OLED_I2C_WriteBuffer) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTOP) for I2C_GenerateSTOP
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_GetFlagStatus) for I2C_GetFlagStatus
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTART) for I2C_GenerateSTART
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_CheckEvent) for I2C_CheckEvent
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_Send7bitAddress) for I2C_Send7bitAddress
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_SendData) for I2C_SendData
    oled_1.o(i.OLED_I2C_WriteCmd) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTOP) for I2C_GenerateSTOP
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_GetFlagStatus) for I2C_GetFlagStatus
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTART) for I2C_GenerateSTART
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_CheckEvent) for I2C_CheckEvent
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_Send7bitAddress) for I2C_Send7bitAddress
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_SendData) for I2C_SendData
    oled_1.o(i.OLED_I2C_WriteData) refers to stm32f4xx_i2c.o(i.I2C_GenerateSTOP) for I2C_GenerateSTOP
    oled_1.o(i.OLED_Init) refers to oled_1.o(i.OLED_I2C_Config) for OLED_I2C_Config
    oled_1.o(i.OLED_Init) refers to systick.o(i.Delay_ms) for Delay_ms
    oled_1.o(i.OLED_Init) refers to oled_1.o(i.OLED_I2C_WriteCmd) for OLED_I2C_WriteCmd
    oled_1.o(i.OLED_Init) refers to oled_1.o(i.OLED_Clear) for OLED_Clear
    oled_1.o(i.OLED_Init) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_IsReady) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_Refresh) refers to oled_1.o(i.OLED_I2C_WriteCmd) for OLED_I2C_WriteCmd
    oled_1.o(i.OLED_Refresh) refers to oled_1.o(i.OLED_I2C_WriteBuffer) for OLED_I2C_WriteBuffer
    oled_1.o(i.OLED_Refresh) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_Refresh) refers to oled_1.o(.bss) for s_oled_buffer
    oled_1.o(i.OLED_SetPos) refers to oled_1.o(i.OLED_I2C_WriteCmd) for OLED_I2C_WriteCmd
    oled_1.o(i.OLED_ShowChar) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_ShowChar) refers to oled_1.o(.constdata) for s_font_6x8
    oled_1.o(i.OLED_ShowChar) refers to oled_1.o(.bss) for s_oled_buffer
    oled_1.o(i.OLED_ShowFloat) refers to _printf_pad.o(.text) for _printf_pre_padding
    oled_1.o(i.OLED_ShowFloat) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_1.o(i.OLED_ShowFloat) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    oled_1.o(i.OLED_ShowFloat) refers to _printf_dec.o(.text) for _printf_int_dec
    oled_1.o(i.OLED_ShowFloat) refers to __2sprintf.o(.text) for __2sprintf
    oled_1.o(i.OLED_ShowFloat) refers to oled_1.o(i.OLED_ShowString) for OLED_ShowString
    oled_1.o(i.OLED_ShowFloat) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_ShowNum) refers to oled_1.o(i.OLED_ShowString) for OLED_ShowString
    oled_1.o(i.OLED_ShowNum) refers to oled_1.o(.data) for g_oled_initialized
    oled_1.o(i.OLED_ShowString) refers to oled_1.o(i.OLED_ShowChar) for OLED_ShowChar
    oled_1.o(i.OLED_ShowString) refers to oled_1.o(.data) for g_oled_initialized
    fft.o(i.FFT_ComputeReal) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    fft.o(i.FFT_ComputeReal) refers to arm_rfft_fast_init_f32.o(.text) for arm_rfft_fast_init_f32
    fft.o(i.FFT_ComputeReal) refers to arm_rfft_fast_f32.o(.text) for arm_rfft_fast_f32
    fft.o(i.FFT_ComputeReal) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    fft.o(i.FFT_ComputeReal) refers to arm_max_f32.o(.text) for arm_max_f32
    fft.o(i.FFT_ComputeReal) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_ComputeReal) refers to fft.o(.data) for g_fft_computation_complete
    fft.o(i.FFT_DeInit) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_GetResult) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fft.o(i.FFT_GetResult) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fft.o(i.FFT_GetStats) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_Init) refers to fft.o(i.FFT_ValidateSize) for FFT_ValidateSize
    fft.o(i.FFT_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fft.o(i.FFT_Init) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_PeakDetection) refers to fft.o(.bss) for g_fft_handle
    fft.o(i.FFT_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fft.o(i.FFT_ResetStats) refers to fft.o(.bss) for g_fft_handle
    dac8552.o(i.DAC8552_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dac8552.o(i.DAC8552_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    dac8552.o(i.DAC8552_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    dac8552.o(i.DAC8552_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    dac8552.o(i.DAC8552_Init) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    dac8552.o(i.DAC8552_Init) refers to dac8552.o(i.DAC8552_Delay_us) for DAC8552_Delay_us
    dac8552.o(i.DAC8552_Init) refers to dac8552.o(i.DAC8552_Test) for DAC8552_Test
    dac8552.o(i.DAC8552_SPI_SendByte) refers to stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    dac8552.o(i.DAC8552_SPI_SendByte) refers to stm32f4xx_spi.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    dac8552.o(i.DAC8552_SetVoltage) refers to dac8552.o(i.DAC8552_Write) for DAC8552_Write
    dac8552.o(i.DAC8552_Test) refers to dac8552.o(i.DAC8552_Write) for DAC8552_Write
    dac8552.o(i.DAC8552_Test) refers to dac8552.o(i.DAC8552_Delay_us) for DAC8552_Delay_us
    dac8552.o(i.DAC8552_Write) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    dac8552.o(i.DAC8552_Write) refers to dac8552.o(i.DAC8552_Delay_us) for DAC8552_Delay_us
    dac8552.o(i.DAC8552_Write) refers to dac8552.o(i.DAC8552_SPI_SendByte) for DAC8552_SPI_SendByte
    dac8552.o(i.DAC8552_Write) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    ad7606.o(i.AD7606_Init) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    ad7606.o(i.AD7606_Init) refers to ad7606.o(i.AD7606_Reset) for AD7606_Reset
    ad7606.o(i.AD7606_Init) refers to ad7606.o(i.AD7606_Test) for AD7606_Test
    ad7606.o(i.AD7606_IsBusy) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ad7606.o(i.AD7606_ReadData) refers to ad7606.o(i.AD7606_StartConversion) for AD7606_StartConversion
    ad7606.o(i.AD7606_ReadData) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ad7606.o(i.AD7606_ReadData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad7606.o(i.AD7606_ReadData) refers to ad7606.o(i.AD7606_Delay_us) for AD7606_Delay_us
    ad7606.o(i.AD7606_ReadData) refers to ad7606.o(i.AD7606_SPI_ReadWord) for AD7606_SPI_ReadWord
    ad7606.o(i.AD7606_ReadData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad7606.o(i.AD7606_Reset) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad7606.o(i.AD7606_Reset) refers to ad7606.o(i.AD7606_Delay_us) for AD7606_Delay_us
    ad7606.o(i.AD7606_Reset) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad7606.o(i.AD7606_SPI_ReadWord) refers to stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    ad7606.o(i.AD7606_SPI_ReadWord) refers to stm32f4xx_spi.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    ad7606.o(i.AD7606_SPI_ReadWord) refers to stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    ad7606.o(i.AD7606_StartConversion) refers to ad7606.o(i.AD7606_IsBusy) for AD7606_IsBusy
    ad7606.o(i.AD7606_StartConversion) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad7606.o(i.AD7606_StartConversion) refers to ad7606.o(i.AD7606_Delay_us) for AD7606_Delay_us
    ad7606.o(i.AD7606_StartConversion) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad7606.o(i.AD7606_Test) refers to ad7606.o(i.AD7606_ReadData) for AD7606_ReadData
    cd4052.o(i.CD4052_GetGain) refers to cd4052.o(.data) for current_gain_level
    cd4052.o(i.CD4052_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    cd4052.o(i.CD4052_Init) refers to cd4052.o(i.CD4052_SetGain) for CD4052_SetGain
    cd4052.o(i.CD4052_SetGain) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    cd4052.o(i.CD4052_SetGain) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    cd4052.o(i.CD4052_SetGain) refers to cd4052.o(.data) for current_gain_level
    arm_rfft_fast_f32.o(.text) refers to arm_cfft_f32.o(.text) for arm_cfft_f32
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable2048
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_4096
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable1024
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_2048
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable512
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_512
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_1024
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable256
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_256
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_512
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable128
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_128
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_256
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable64
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_64
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_128
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable32
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_32
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_64
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable16
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_16
    arm_rfft_fast_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_32
    arm_cfft_f32.o(.text) refers to arm_cfft_radix8_f32.o(.text) for arm_radix8_butterfly_f32
    arm_cfft_f32.o(.text) refers to arm_bitreversal2.o(. text) for arm_bitreversal_32
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to dflt_clz.o(x$fpl$dfltn) for __dflt_normalise
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers to except.o(x$fpl$exception) for __fpl_exception
    fnaninf.o(x$fpl$fnaninf) refers to funder_clz.o(x$fpl$funder) for __funder_d
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2e) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_e2d) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    except.o(x$fpl$exception) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    except.o(x$fpl$exception) refers to trapv.o(x$fpl$trapveneer) for _fp_trapveneer
    except.o(x$fpl$exception) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    funder_clz.o(x$fpl$funder) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder_clz.o(x$fpl$funder) refers to except.o(x$fpl$exception) for __fpl_exception
    funder_clz.o(x$fpl$funder) refers to trapv.o(x$fpl$trapveneer) for _fp_trapveneer
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    trapv.o(x$fpl$trapveneer) refers to _fptrap.o(.text) for _fp_trap
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    _fptrap.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.G_Comprehensive_Test), (352 bytes).
    Removing main.o(i.SystemClock_Config), (2 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (152 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (172 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (100 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (192 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (120 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (128 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (168 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (368 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (140 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (88 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (220 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (304 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (396 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (328 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (428 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (580 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (112 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (248 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (312 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (100 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (92 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (464 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (232 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (108 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (112 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (76 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (312 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (148 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (264 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1778 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1308 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (250 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (226 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (282 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (258 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (96 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (116 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (124 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (136 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (128 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (100 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (64 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (172 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (92 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (108 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (264 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dfsdm.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (184 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (580 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (208 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (80 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (120 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (124 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (132 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (452 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (60 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (84 bytes).
    Removing stm32f4xx_dsi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (60 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (68 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (36 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (36 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (228 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (120 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (592 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (112 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit), (904 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (96 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (532 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (164 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (280 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (108 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (104 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (232 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (76 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (180 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (244 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (132 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (92 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (136 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (112 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (116 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (80 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (60 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (64 bytes).
    Removing stm32f4xx_lptim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (144 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (72 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (180 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (96 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (104 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (572 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (60 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (76 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (540 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (106 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (160 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (116 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (48 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (72 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (132 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (140 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (88 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (56 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (164 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (184 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (80 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (72 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (80 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (180 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (232 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (128 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (152 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (192 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (172 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (144 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (684 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (420 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (452 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (112 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (72 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (116 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (76 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (68 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (224 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (132 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (140 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (432 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (180 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (30 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (96 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (64 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (268 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (22 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (80 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (104 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (208 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (180 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (120 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (276 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (608 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (156 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (136 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (104 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (180 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (248 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (220 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (204 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (160 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (112 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (68 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (60 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (256 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (112 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (316 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (516 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (208 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (280 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (528 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (96 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (444 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (336 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (156 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (320 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (172 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (152 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (252 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (220 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (220 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (212 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (344 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiveData), (152 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (156 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (180 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (104 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (164 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (184 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (84 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (84 bytes).
    Removing adc_dma.o(.rev16_text), (4 bytes).
    Removing adc_dma.o(.revsh_text), (4 bytes).
    Removing adc_dma.o(.rrx_text), (6 bytes).
    Removing adc_dma.o(i.ADC_ApplyCalibration), (136 bytes).
    Removing adc_dma.o(i.ADC_GetChannelValue), (60 bytes).
    Removing adc_dma.o(i.ADC_GetChannelVoltage_mV), (28 bytes).
    Removing adc_dma.o(i.ADC_GetMultiChannelData), (124 bytes).
    Removing adc_dma.o(i.ADC_SetSampleRate), (52 bytes).
    Removing adc_dma.o(i.ADC_Stop_Acquisition), (40 bytes).
    Removing parallel_adc.o(.rev16_text), (4 bytes).
    Removing parallel_adc.o(.revsh_text), (4 bytes).
    Removing parallel_adc.o(.rrx_text), (6 bytes).
    Removing parallel_adc.o(i.ParallelADC_Buffer_Write), (108 bytes).
    Removing parallel_adc.o(i.ParallelADC_GetSampleRate), (12 bytes).
    Removing parallel_adc.o(i.ParallelADC_GetState), (12 bytes).
    Removing parallel_adc.o(i.ParallelADC_GetStats), (28 bytes).
    Removing parallel_adc.o(i.ParallelADC_ReadMultiple), (52 bytes).
    Removing parallel_adc.o(i.ParallelADC_SetTriggerEdge), (36 bytes).
    Removing parallel_adc.o(i.ParallelADC_Stop), (16 bytes).
    Removing parallel_adc.o(i.ParallelADC_UpdateStats), (196 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.Delay_s), (24 bytes).
    Removing systick.o(i.Delay_us), (80 bytes).
    Removing systick.o(i.SysTick_Calibrate), (96 bytes).
    Removing systick.o(i.SysTick_GetCalibratedDelay), (72 bytes).
    Removing systick.o(i.SysTick_GetStats), (24 bytes).
    Removing systick.o(i.SysTick_GetTimestamp_us), (56 bytes).
    Removing systick.o(i.SysTick_NonBlocking_Init), (32 bytes).
    Removing systick.o(i.SysTick_NonBlocking_IsCompleted), (48 bytes).
    Removing systick.o(i.SysTick_SetTemperatureCompensation), (24 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.RingBuffer_Read), (60 bytes).
    Removing usart.o(i.RingBuffer_Write), (68 bytes).
    Removing usart.o(i.USART_GetStats), (28 bytes).
    Removing usart.o(i.USART_IsTxComplete), (12 bytes).
    Removing usart.o(i.USART_Module_ReceiveData), (72 bytes).
    Removing usart.o(i.USART_ReceiveByte), (80 bytes).
    Removing usart.o(i.USART_SendData_DMA), (104 bytes).
    Removing usart.o(i.USART_SendHex), (92 bytes).
    Removing usart.o(i.USART_WaitTxComplete), (44 bytes).
    Removing usart.o(i.fgetc), (28 bytes).
    Removing usart.o(i.fputc), (16 bytes).
    Removing dds_wavegen.o(.rev16_text), (4 bytes).
    Removing dds_wavegen.o(.revsh_text), (4 bytes).
    Removing dds_wavegen.o(.rrx_text), (6 bytes).
    Removing dds_wavegen.o(i.DDS_CalculateTHD), (12 bytes).
    Removing dds_wavegen.o(i.DDS_ConfigModulation), (56 bytes).
    Removing dds_wavegen.o(i.DDS_EnableModulation), (12 bytes).
    Removing dds_wavegen.o(i.DDS_GetCurrentOutput), (10 bytes).
    Removing dds_wavegen.o(i.DDS_SetAmplitude), (56 bytes).
    Removing dds_wavegen.o(i.DDS_SetCustomWave), (44 bytes).
    Removing dds_wavegen.o(i.DDS_SetOffset), (28 bytes).
    Removing dds_wavegen.o(i.DDS_SetPhase), (68 bytes).
    Removing dds_wavegen.o(i.DDS_Stop), (48 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.Key_DeInit), (12 bytes).
    Removing key.o(i.Key_GetState), (36 bytes).
    Removing key.o(i.Key_IsPressed), (20 bytes).
    Removing key.o(i.Key_IsReady), (12 bytes).
    Removing key.o(i.Key_WaitPress), (64 bytes).
    Removing key.o(i.Key_WaitRelease), (68 bytes).
    Removing oled_1.o(.rev16_text), (4 bytes).
    Removing oled_1.o(.revsh_text), (4 bytes).
    Removing oled_1.o(.rrx_text), (6 bytes).
    Removing oled_1.o(i.OLED_DrawLine), (244 bytes).
    Removing oled_1.o(i.OLED_DrawPoint), (108 bytes).
    Removing oled_1.o(i.OLED_DrawRect), (208 bytes).
    Removing oled_1.o(i.OLED_I2C_WriteData), (136 bytes).
    Removing oled_1.o(i.OLED_IsReady), (12 bytes).
    Removing oled_1.o(i.OLED_SetPos), (36 bytes).
    Removing oled_1.o(i.OLED_ShowFloat), (140 bytes).
    Removing oled_1.o(i.OLED_ShowNum), (176 bytes).
    Removing fft.o(.rev16_text), (4 bytes).
    Removing fft.o(.revsh_text), (4 bytes).
    Removing fft.o(.rrx_text), (6 bytes).
    Removing fft.o(i.FFT_ComputeReal), (344 bytes).
    Removing fft.o(i.FFT_DeInit), (24 bytes).
    Removing fft.o(i.FFT_GetResult), (48 bytes).
    Removing fft.o(i.FFT_GetStats), (28 bytes).
    Removing fft.o(i.FFT_Init), (92 bytes).
    Removing fft.o(i.FFT_PeakDetection), (168 bytes).
    Removing fft.o(i.FFT_ResetStats), (16 bytes).
    Removing fft.o(i.FFT_ValidateSize), (78 bytes).
    Removing fft.o(.bss), (32908 bytes).
    Removing fft.o(.data), (1 bytes).
    Removing dac8552.o(.rev16_text), (4 bytes).
    Removing dac8552.o(.revsh_text), (4 bytes).
    Removing dac8552.o(.rrx_text), (6 bytes).
    Removing ad7606.o(.rev16_text), (4 bytes).
    Removing ad7606.o(.revsh_text), (4 bytes).
    Removing ad7606.o(.rrx_text), (6 bytes).
    Removing cd4052.o(.rev16_text), (4 bytes).
    Removing cd4052.o(.revsh_text), (4 bytes).
    Removing cd4052.o(.rrx_text), (6 bytes).
    Removing cd4052.o(i.CD4052_GetGain), (12 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rrx_text), (6 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (252 bytes).
    Removing arm_rfft_fast_f32.o(.rev16_text), (4 bytes).
    Removing arm_rfft_fast_f32.o(.revsh_text), (4 bytes).
    Removing arm_rfft_fast_f32.o(.rrx_text), (6 bytes).
    Removing arm_rfft_fast_f32.o(.text), (402 bytes).
    Removing arm_rfft_fast_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_rfft_fast_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_rfft_fast_init_f32.o(.rrx_text), (6 bytes).
    Removing arm_rfft_fast_init_f32.o(.text), (296 bytes).
    Removing arm_max_f32.o(.rev16_text), (4 bytes).
    Removing arm_max_f32.o(.revsh_text), (4 bytes).
    Removing arm_max_f32.o(.rrx_text), (6 bytes).
    Removing arm_max_f32.o(.text), (216 bytes).
    Removing arm_cfft_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_f32.o(.text), (1826 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (32768 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (24 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (224 bytes).
    Removing arm_common_tables.o(.constdata), (480 bytes).
    Removing arm_common_tables.o(.constdata), (960 bytes).
    Removing arm_common_tables.o(.constdata), (1984 bytes).
    Removing arm_common_tables.o(.constdata), (3968 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (1026 bytes).
    Removing arm_cfft_radix8_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix8_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix8_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_radix8_f32.o(.text), (1244 bytes).
    Removing arm_bitreversal2.o(. text), (192 bytes).

969 unused section(s) (total 342317 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _fptrap.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/except.s                        0x00000000   Number         0  except.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/funder.s                        0x00000000   Number         0  funder_clz.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\..\Source\CommonTables\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\..\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\..\Source\StatisticsFunctions\arm_max_f32.c 0x00000000   Number         0  arm_max_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_f32.c 0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_rfft_fast_f32.c 0x00000000   Number         0  arm_rfft_fast_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_rfft_fast_init_f32.c 0x00000000   Number         0  arm_rfft_fast_init_f32.o ABSOLUTE
    ..\\..\\Source\\CommonTables\\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\..\\Source\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\..\\Source\\StatisticsFunctions\\arm_max_f32.c 0x00000000   Number         0  arm_max_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_bitreversal2.S 0x00000000   Number         0  arm_bitreversal2.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_f32.c 0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_rfft_fast_f32.c 0x00000000   Number         0  arm_rfft_fast_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_rfft_fast_init_f32.c 0x00000000   Number         0  arm_rfft_fast_init_f32.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_adc.c                 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\\stm32f4xx_can.c                 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\\stm32f4xx_cec.c                 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\\stm32f4xx_crc.c                 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\\stm32f4xx_cryp.c                0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\\stm32f4xx_cryp_aes.c            0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\\stm32f4xx_cryp_des.c            0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\\stm32f4xx_cryp_tdes.c           0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\\stm32f4xx_dac.c                 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\\stm32f4xx_dbgmcu.c              0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\\stm32f4xx_dcmi.c                0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\\stm32f4xx_dfsdm.c               0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\\stm32f4xx_dma.c                 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\\stm32f4xx_dma2d.c               0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\\stm32f4xx_dsi.c                 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\\stm32f4xx_exti.c                0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_flash_ramfunc.c       0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\\stm32f4xx_fmpi2c.c              0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\\stm32f4xx_fsmc.c                0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_hash.c                0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\\stm32f4xx_hash_md5.c            0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\\stm32f4xx_hash_sha1.c           0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\\stm32f4xx_i2c.c                 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\\stm32f4xx_iwdg.c                0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\\stm32f4xx_lptim.c               0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\\stm32f4xx_ltdc.c                0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\\stm32f4xx_pwr.c                 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\\stm32f4xx_qspi.c                0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\\stm32f4xx_rng.c                 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\\stm32f4xx_rtc.c                 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\\stm32f4xx_sai.c                 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\\stm32f4xx_sdio.c                0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\\stm32f4xx_spdifrx.c             0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\\stm32f4xx_spi.c                 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\\stm32f4xx_syscfg.c              0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\\stm32f4xx_tim.c                 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\\stm32f4xx_usart.c               0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\\stm32f4xx_wwdg.c                0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_adc.c                  0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\stm32f4xx_can.c                  0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\stm32f4xx_cec.c                  0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\stm32f4xx_crc.c                  0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\stm32f4xx_cryp.c                 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\stm32f4xx_cryp_aes.c             0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\stm32f4xx_cryp_des.c             0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\stm32f4xx_cryp_tdes.c            0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\stm32f4xx_dac.c                  0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\stm32f4xx_dbgmcu.c               0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\stm32f4xx_dcmi.c                 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\stm32f4xx_dfsdm.c                0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\stm32f4xx_dma.c                  0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\stm32f4xx_dma2d.c                0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\stm32f4xx_dsi.c                  0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\stm32f4xx_exti.c                 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_flash_ramfunc.c        0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\stm32f4xx_fmpi2c.c               0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\stm32f4xx_fsmc.c                 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_hash.c                 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\stm32f4xx_hash_md5.c             0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\stm32f4xx_hash_sha1.c            0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\stm32f4xx_i2c.c                  0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\stm32f4xx_iwdg.c                 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\stm32f4xx_lptim.c                0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\stm32f4xx_ltdc.c                 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\stm32f4xx_pwr.c                  0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\stm32f4xx_qspi.c                 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\stm32f4xx_rng.c                  0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\stm32f4xx_rtc.c                  0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\stm32f4xx_sai.c                  0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\stm32f4xx_sdio.c                 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\stm32f4xx_spdifrx.c              0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\stm32f4xx_spi.c                  0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\stm32f4xx_syscfg.c               0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\stm32f4xx_tim.c                  0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\stm32f4xx_usart.c                0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\stm32f4xx_wwdg.c                 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Modules\Acquisition\ad7606.c             0x00000000   Number         0  ad7606.o ABSOLUTE
    Modules\Acquisition\adc_dma.c            0x00000000   Number         0  adc_dma.o ABSOLUTE
    Modules\Acquisition\parallel_adc.c       0x00000000   Number         0  parallel_adc.o ABSOLUTE
    Modules\Core\systick.c                   0x00000000   Number         0  systick.o ABSOLUTE
    Modules\Core\usart.c                     0x00000000   Number         0  usart.o ABSOLUTE
    Modules\Generation\dac8552.c             0x00000000   Number         0  dac8552.o ABSOLUTE
    Modules\Generation\dds_wavegen.c         0x00000000   Number         0  dds_wavegen.o ABSOLUTE
    Modules\Interface\cd4052.c               0x00000000   Number         0  cd4052.o ABSOLUTE
    Modules\Interface\key.c                  0x00000000   Number         0  key.o ABSOLUTE
    Modules\Interface\oled.c                 0x00000000   Number         0  oled_1.o ABSOLUTE
    Modules\Processing\fft.c                 0x00000000   Number         0  fft.o ABSOLUTE
    Modules\\Acquisition\\ad7606.c           0x00000000   Number         0  ad7606.o ABSOLUTE
    Modules\\Acquisition\\adc_dma.c          0x00000000   Number         0  adc_dma.o ABSOLUTE
    Modules\\Acquisition\\parallel_adc.c     0x00000000   Number         0  parallel_adc.o ABSOLUTE
    Modules\\Core\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    Modules\\Core\\usart.c                   0x00000000   Number         0  usart.o ABSOLUTE
    Modules\\Generation\\dac8552.c           0x00000000   Number         0  dac8552.o ABSOLUTE
    Modules\\Generation\\dds_wavegen.c       0x00000000   Number         0  dds_wavegen.o ABSOLUTE
    Modules\\Interface\\cd4052.c             0x00000000   Number         0  cd4052.o ABSOLUTE
    Modules\\Interface\\key.c                0x00000000   Number         0  key.o ABSOLUTE
    Modules\\Interface\\oled.c               0x00000000   Number         0  oled_1.o ABSOLUTE
    Modules\\Processing\\fft.c               0x00000000   Number         0  fft.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\bsp.c                              0x00000000   Number         0  bsp.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\bsp.c                               0x00000000   Number         0  bsp.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsnprintf.o(.text)
    .text                                    0x08000438   Section        0  __2sprintf.o(.text)
    .text                                    0x08000464   Section        0  _printf_pad.o(.text)
    .text                                    0x080004b4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800052c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080006b4   Section        0  strlen.o(.text)
    .text                                    0x080006f2   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000756   Section       68  rt_memclr.o(.text)
    .text                                    0x0800079a   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080007e8   Section        0  heapauxi.o(.text)
    .text                                    0x080007ee   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000812   Section        0  _printf_str.o(.text)
    .text                                    0x08000864   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000916   Section        0  _printf_charcount.o(.text)
    .text                                    0x0800093e   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000941   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000d5c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000d5d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000d8c   Section        0  _sputc.o(.text)
    .text                                    0x08000d96   Section        0  _snputc.o(.text)
    .text                                    0x08000da8   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000e64   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000ee0   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000ee1   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000f50   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000f51   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000fe4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000fec   Section      138  lludiv10.o(.text)
    .text                                    0x08001078   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001374   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080013f4   Section        0  _printf_char.o(.text)
    .text                                    0x08001420   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800144c   Section        0  bigflt0.o(.text)
    .text                                    0x08001530   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001570   Section        8  libspace.o(.text)
    .text                                    0x08001578   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080015c4   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080015d4   Section        0  exit.o(.text)
    .text                                    0x080015e6   Section        0  defsig_fpe_outer.o(.text)
    .text                                    0x080015f4   Section      128  strcmpv7m.o(.text)
    .text                                    0x08001674   Section        0  _fptrap.o(.text)
    .text                                    0x080016a4   Section        0  defsig_exit.o(.text)
    .text                                    0x080016b0   Section        0  defsig_fpe_inner.o(.text)
    .text                                    0x0800175c   Section        0  sys_exit.o(.text)
    .text                                    0x08001768   Section        0  defsig_general.o(.text)
    .text                                    0x0800179a   Section        0  sys_wrch.o(.text)
    .text                                    0x080017a8   Section        2  use_no_semi.o(.text)
    .text                                    0x080017aa   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x080017aa   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080017e8   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800182e   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800188e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001bc6   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001ca2   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001ccc   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001cf6   Section      580  btod.o(CL$$btod_mult_common)
    i.AD7606_Delay_us                        0x08001f3a   Section        0  ad7606.o(i.AD7606_Delay_us)
    AD7606_Delay_us                          0x08001f3b   Thumb Code    24  ad7606.o(i.AD7606_Delay_us)
    i.AD7606_Init                            0x08001f54   Section        0  ad7606.o(i.AD7606_Init)
    i.AD7606_IsBusy                          0x08002080   Section        0  ad7606.o(i.AD7606_IsBusy)
    i.AD7606_ReadData                        0x08002090   Section        0  ad7606.o(i.AD7606_ReadData)
    i.AD7606_Reset                           0x08002120   Section        0  ad7606.o(i.AD7606_Reset)
    i.AD7606_SPI_ReadWord                    0x0800214c   Section        0  ad7606.o(i.AD7606_SPI_ReadWord)
    AD7606_SPI_ReadWord                      0x0800214d   Thumb Code    84  ad7606.o(i.AD7606_SPI_ReadWord)
    i.AD7606_StartConversion                 0x080021a4   Section        0  ad7606.o(i.AD7606_StartConversion)
    i.AD7606_Test                            0x080021dc   Section        0  ad7606.o(i.AD7606_Test)
    i.ADC1_DMA_Init                          0x08002200   Section        0  adc_dma.o(i.ADC1_DMA_Init)
    i.ADC_Calibrate                          0x080022b4   Section        0  adc_dma.o(i.ADC_Calibrate)
    i.ADC_ClearITPendingBit                  0x08002368   Section        0  stm32f4xx_adc.o(i.ADC_ClearITPendingBit)
    i.ADC_Cmd                                0x080023e4   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x08002454   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_Config                             0x080025d0   Section        0  adc_dma.o(i.ADC_Config)
    ADC_Config                               0x080025d1   Thumb Code   146  adc_dma.o(i.ADC_Config)
    i.ADC_DMACmd                             0x08002668   Section        0  stm32f4xx_adc.o(i.ADC_DMACmd)
    i.ADC_DMARequestAfterLastTransferCmd     0x080026d8   Section        0  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    i.ADC_DMA_Config                         0x08002748   Section        0  adc_dma.o(i.ADC_DMA_Config)
    ADC_DMA_Config                           0x08002749   Thumb Code   140  adc_dma.o(i.ADC_DMA_Config)
    i.ADC_GPIO_Config                        0x080027e4   Section        0  adc_dma.o(i.ADC_GPIO_Config)
    ADC_GPIO_Config                          0x080027e5   Thumb Code    36  adc_dma.o(i.ADC_GPIO_Config)
    i.ADC_GetITStatus                        0x0800280c   Section        0  stm32f4xx_adc.o(i.ADC_GetITStatus)
    i.ADC_GetStats                           0x080028ac   Section        0  adc_dma.o(i.ADC_GetStats)
    i.ADC_IRQHandler                         0x08002920   Section        0  adc_dma.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x0800296c   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_NVIC_Config                        0x08002b2c   Section        0  adc_dma.o(i.ADC_NVIC_Config)
    ADC_NVIC_Config                          0x08002b2d   Thumb Code    58  adc_dma.o(i.ADC_NVIC_Config)
    i.ADC_ProcessData                        0x08002b68   Section        0  adc_dma.o(i.ADC_ProcessData)
    i.ADC_RegularChannelConfig               0x08002c14   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetStats                         0x08002dd0   Section        0  adc_dma.o(i.ADC_ResetStats)
    i.ADC_SoftwareStartConv                  0x08002df8   Section        0  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    i.ADC_Start_Acquisition                  0x08002e48   Section        0  adc_dma.o(i.ADC_Start_Acquisition)
    i.ADC_Timer_Config                       0x08002ea0   Section        0  adc_dma.o(i.ADC_Timer_Config)
    ADC_Timer_Config                         0x08002ea1   Thumb Code    62  adc_dma.o(i.ADC_Timer_Config)
    i.ADC_UpdateStats                        0x08002ee4   Section        0  adc_dma.o(i.ADC_UpdateStats)
    ADC_UpdateStats                          0x08002ee5   Thumb Code   110  adc_dma.o(i.ADC_UpdateStats)
    i.BSP_Init                               0x08002f58   Section        0  bsp.o(i.BSP_Init)
    i.BusFault_Handler                       0x08002f8c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CD4052_Init                            0x08002f90   Section        0  cd4052.o(i.CD4052_Init)
    i.CD4052_SetGain                         0x08002fc4   Section        0  cd4052.o(i.CD4052_SetGain)
    i.DAC8552_Delay_us                       0x08003038   Section        0  dac8552.o(i.DAC8552_Delay_us)
    DAC8552_Delay_us                         0x08003039   Thumb Code    24  dac8552.o(i.DAC8552_Delay_us)
    i.DAC8552_Init                           0x08003050   Section        0  dac8552.o(i.DAC8552_Init)
    i.DAC8552_SPI_SendByte                   0x08003128   Section        0  dac8552.o(i.DAC8552_SPI_SendByte)
    DAC8552_SPI_SendByte                     0x08003129   Thumb Code    76  dac8552.o(i.DAC8552_SPI_SendByte)
    i.DAC8552_SetVoltage                     0x08003178   Section        0  dac8552.o(i.DAC8552_SetVoltage)
    i.DAC8552_Test                           0x080031e4   Section        0  dac8552.o(i.DAC8552_Test)
    i.DAC8552_Write                          0x08003214   Section        0  dac8552.o(i.DAC8552_Write)
    i.DAC_Cmd                                0x080032a4   Section        0  stm32f4xx_dac.o(i.DAC_Cmd)
    i.DAC_DMACmd                             0x0800330c   Section        0  stm32f4xx_dac.o(i.DAC_DMACmd)
    i.DAC_Init                               0x08003378   Section        0  stm32f4xx_dac.o(i.DAC_Init)
    i.DDS_DAC_Config                         0x080034fc   Section        0  dds_wavegen.o(i.DDS_DAC_Config)
    DDS_DAC_Config                           0x080034fd   Thumb Code    46  dds_wavegen.o(i.DDS_DAC_Config)
    i.DDS_DMA_Config                         0x0800352c   Section        0  dds_wavegen.o(i.DDS_DMA_Config)
    DDS_DMA_Config                           0x0800352d   Thumb Code   112  dds_wavegen.o(i.DDS_DMA_Config)
    i.DDS_GPIO_Config                        0x080035a8   Section        0  dds_wavegen.o(i.DDS_GPIO_Config)
    DDS_GPIO_Config                          0x080035a9   Thumb Code    36  dds_wavegen.o(i.DDS_GPIO_Config)
    i.DDS_GenerateWaveTable                  0x080035d0   Section        0  dds_wavegen.o(i.DDS_GenerateWaveTable)
    DDS_GenerateWaveTable                    0x080035d1   Thumb Code    68  dds_wavegen.o(i.DDS_GenerateWaveTable)
    i.DDS_GetNextSample                      0x08003628   Section        0  dds_wavegen.o(i.DDS_GetNextSample)
    DDS_GetNextSample                        0x08003629   Thumb Code   104  dds_wavegen.o(i.DDS_GetNextSample)
    i.DDS_GetStats                           0x08003694   Section        0  dds_wavegen.o(i.DDS_GetStats)
    i.DDS_Init                               0x08003714   Section        0  dds_wavegen.o(i.DDS_Init)
    i.DDS_LinearInterpolation                0x080037d8   Section        0  dds_wavegen.o(i.DDS_LinearInterpolation)
    DDS_LinearInterpolation                  0x080037d9   Thumb Code    58  dds_wavegen.o(i.DDS_LinearInterpolation)
    i.DDS_NVIC_Config                        0x08003818   Section        0  dds_wavegen.o(i.DDS_NVIC_Config)
    DDS_NVIC_Config                          0x08003819   Thumb Code    60  dds_wavegen.o(i.DDS_NVIC_Config)
    i.DDS_ProcessModulation                  0x08003854   Section        0  dds_wavegen.o(i.DDS_ProcessModulation)
    DDS_ProcessModulation                    0x08003855   Thumb Code   102  dds_wavegen.o(i.DDS_ProcessModulation)
    i.DDS_ResetStats                         0x080038c4   Section        0  dds_wavegen.o(i.DDS_ResetStats)
    i.DDS_SetFrequency                       0x080038e0   Section        0  dds_wavegen.o(i.DDS_SetFrequency)
    i.DDS_SetWaveType                        0x08003910   Section        0  dds_wavegen.o(i.DDS_SetWaveType)
    i.DDS_Start                              0x08003940   Section        0  dds_wavegen.o(i.DDS_Start)
    i.DDS_TIM_Config                         0x0800398c   Section        0  dds_wavegen.o(i.DDS_TIM_Config)
    DDS_TIM_Config                           0x0800398d   Thumb Code    62  dds_wavegen.o(i.DDS_TIM_Config)
    i.DDS_UpdateFrequencyWord                0x080039d4   Section        0  dds_wavegen.o(i.DDS_UpdateFrequencyWord)
    DDS_UpdateFrequencyWord                  0x080039d5   Thumb Code    26  dds_wavegen.o(i.DDS_UpdateFrequencyWord)
    i.DMA1_Stream5_IRQHandler                0x080039f4   Section        0  dds_wavegen.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream0_IRQHandler                0x08003a78   Section        0  adc_dma.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08003b0c   Section        0  usart.o(i.DMA2_Stream2_IRQHandler)
    i.DMA2_Stream7_IRQHandler                0x08003b7c   Section        0  usart.o(i.DMA2_Stream7_IRQHandler)
    i.DMA_ClearITPendingBit                  0x08003be0   Section        0  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x08003ce0   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x08003db8   Section        0  stm32f4xx_dma.o(i.DMA_DeInit)
    i.DMA_DoubleBufferModeCmd                0x08003fb4   Section        0  stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd)
    i.DMA_DoubleBufferModeConfig             0x0800408c   Section        0  stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig)
    i.DMA_GetCurrDataCounter                 0x08004168   Section        0  stm32f4xx_dma.o(i.DMA_GetCurrDataCounter)
    i.DMA_GetITStatus                        0x0800421c   Section        0  stm32f4xx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x080044c8   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x080045d8   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DMA_SetCurrDataCounter                 0x08004890   Section        0  stm32f4xx_dma.o(i.DMA_SetCurrDataCounter)
    i.DWT_Init                               0x08004944   Section        0  systick.o(i.DWT_Init)
    i.DebugMon_Handler                       0x080049a0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080049a4   Section        0  systick.o(i.Delay_ms)
    i.EXTI0_IRQHandler                       0x080049d0   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI0_IRQHandler_Internal              0x080049d8   Section        0  parallel_adc.o(i.EXTI0_IRQHandler_Internal)
    i.EXTI_ClearITPendingBit                 0x08004a30   Section        0  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08004a70   Section        0  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08004b34   Section        0  stm32f4xx_exti.o(i.EXTI_Init)
    i.GPIO_Init                              0x08004c40   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08004de8   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08004f90   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x0800509c   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800514c   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.G_Module_Init                          0x080051fc   Section        0  main.o(i.G_Module_Init)
    i.G_Module_Test                          0x0800537c   Section        0  main.o(i.G_Module_Test)
    i.HardFault_Handler                      0x08005538   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_CheckEvent                         0x0800553c   Section        0  stm32f4xx_i2c.o(i.I2C_CheckEvent)
    i.I2C_Cmd                                0x08005678   Section        0  stm32f4xx_i2c.o(i.I2C_Cmd)
    i.I2C_DeInit                             0x080056ec   Section        0  stm32f4xx_i2c.o(i.I2C_DeInit)
    i.I2C_GenerateSTART                      0x08005780   Section        0  stm32f4xx_i2c.o(i.I2C_GenerateSTART)
    i.I2C_GenerateSTOP                       0x080057f4   Section        0  stm32f4xx_i2c.o(i.I2C_GenerateSTOP)
    i.I2C_GetFlagStatus                      0x08005868   Section        0  stm32f4xx_i2c.o(i.I2C_GetFlagStatus)
    i.I2C_Init                               0x08005994   Section        0  stm32f4xx_i2c.o(i.I2C_Init)
    i.I2C_Send7bitAddress                    0x08005b4c   Section        0  stm32f4xx_i2c.o(i.I2C_Send7bitAddress)
    i.I2C_SendData                           0x08005bb8   Section        0  stm32f4xx_i2c.o(i.I2C_SendData)
    i.Key_GPIO_Config                        0x08005c04   Section        0  key.o(i.Key_GPIO_Config)
    Key_GPIO_Config                          0x08005c05   Thumb Code    40  key.o(i.Key_GPIO_Config)
    i.Key_Init                               0x08005c30   Section        0  key.o(i.Key_Init)
    i.Key_ReadRaw                            0x08005c70   Section        0  key.o(i.Key_ReadRaw)
    Key_ReadRaw                              0x08005c71   Thumb Code    54  key.o(i.Key_ReadRaw)
    i.Key_Scan                               0x08005cac   Section        0  key.o(i.Key_Scan)
    i.MemManage_Handler                      0x08005d44   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Module_Init_Demo                       0x08005d48   Section        0  main.o(i.Module_Init_Demo)
    i.Module_Test_Demo                       0x080060ac   Section        0  main.o(i.Module_Test_Demo)
    i.NMI_Handler                            0x080061c0   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080061c4   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_SetPriority                       0x08006284   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08006285   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x080062ac   Section        0  oled_1.o(i.OLED_Clear)
    i.OLED_I2C_Config                        0x080062d4   Section        0  oled_1.o(i.OLED_I2C_Config)
    OLED_I2C_Config                          0x080062d5   Thumb Code   136  oled_1.o(i.OLED_I2C_Config)
    i.OLED_I2C_WriteBuffer                   0x08006368   Section        0  oled_1.o(i.OLED_I2C_WriteBuffer)
    OLED_I2C_WriteBuffer                     0x08006369   Thumb Code   224  oled_1.o(i.OLED_I2C_WriteBuffer)
    i.OLED_I2C_WriteCmd                      0x08006454   Section        0  oled_1.o(i.OLED_I2C_WriteCmd)
    OLED_I2C_WriteCmd                        0x08006455   Thumb Code   206  oled_1.o(i.OLED_I2C_WriteCmd)
    i.OLED_Init                              0x08006530   Section        0  oled_1.o(i.OLED_Init)
    i.OLED_Refresh                           0x080065e4   Section        0  oled_1.o(i.OLED_Refresh)
    i.OLED_ShowChar                          0x0800665c   Section        0  oled_1.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x080066f0   Section        0  oled_1.o(i.OLED_ShowString)
    i.ParallelADC_Buffer_Init                0x0800674c   Section        0  parallel_adc.o(i.ParallelADC_Buffer_Init)
    ParallelADC_Buffer_Init                  0x0800674d   Thumb Code    32  parallel_adc.o(i.ParallelADC_Buffer_Init)
    i.ParallelADC_Buffer_Read                0x08006774   Section        0  parallel_adc.o(i.ParallelADC_Buffer_Read)
    ParallelADC_Buffer_Read                  0x08006775   Thumb Code    92  parallel_adc.o(i.ParallelADC_Buffer_Read)
    i.ParallelADC_ClearBuffer                0x080067d4   Section        0  parallel_adc.o(i.ParallelADC_ClearBuffer)
    i.ParallelADC_EXTI_Config                0x080067f0   Section        0  parallel_adc.o(i.ParallelADC_EXTI_Config)
    ParallelADC_EXTI_Config                  0x080067f1   Thumb Code    56  parallel_adc.o(i.ParallelADC_EXTI_Config)
    i.ParallelADC_GPIO_Config                0x08006828   Section        0  parallel_adc.o(i.ParallelADC_GPIO_Config)
    ParallelADC_GPIO_Config                  0x08006829   Thumb Code   110  parallel_adc.o(i.ParallelADC_GPIO_Config)
    i.ParallelADC_Init                       0x080068a4   Section        0  parallel_adc.o(i.ParallelADC_Init)
    i.ParallelADC_NVIC_Config                0x080068fc   Section        0  parallel_adc.o(i.ParallelADC_NVIC_Config)
    ParallelADC_NVIC_Config                  0x080068fd   Thumb Code    32  parallel_adc.o(i.ParallelADC_NVIC_Config)
    i.ParallelADC_ReadSingle                 0x0800691c   Section        0  parallel_adc.o(i.ParallelADC_ReadSingle)
    i.ParallelADC_ResetStats                 0x08006930   Section        0  parallel_adc.o(i.ParallelADC_ResetStats)
    i.ParallelADC_Start                      0x08006964   Section        0  parallel_adc.o(i.ParallelADC_Start)
    i.PendSV_Handler                         0x080069a0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x080069a4   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08006a0c   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x08006a70   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x08006ad4   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08006b3c   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.RingBuffer_Init                        0x08006c24   Section        0  usart.o(i.RingBuffer_Init)
    RingBuffer_Init                          0x08006c25   Thumb Code    16  usart.o(i.RingBuffer_Init)
    i.SPI_Cmd                                0x08006c34   Section        0  stm32f4xx_spi.o(i.SPI_Cmd)
    i.SPI_I2S_GetFlagStatus                  0x08006cc4   Section        0  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    i.SPI_I2S_ReceiveData                    0x08006d80   Section        0  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    i.SPI_I2S_SendData                       0x08006df8   Section        0  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    i.SPI_Init                               0x08006e74   Section        0  stm32f4xx_spi.o(i.SPI_Init)
    i.SVC_Handler                            0x08007014   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SYSCFG_EXTILineConfig                  0x08007018   Section        0  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    i.SetSysClock                            0x080070f4   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080070f5   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_GetTick                        0x080071e0   Section        0  systick.o(i.SysTick_GetTick)
    i.SysTick_GetUptime_ms                   0x080071ec   Section        0  systick.o(i.SysTick_GetUptime_ms)
    i.SysTick_Handler                        0x080071f8   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Handler_Internal               0x08007214   Section        0  systick.o(i.SysTick_Handler_Internal)
    i.SysTick_Init                           0x0800724c   Section        0  systick.o(i.SysTick_Init)
    i.SysTick_ResetStats                     0x080072c8   Section        0  systick.o(i.SysTick_ResetStats)
    i.SysTick_UpdateStats                    0x080072e8   Section        0  systick.o(i.SysTick_UpdateStats)
    SysTick_UpdateStats                      0x080072e9   Thumb Code    80  systick.o(i.SysTick_UpdateStats)
    i.SystemInit                             0x08007348   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x080073b0   Section        0  dds_wavegen.o(i.TIM6_DAC_IRQHandler)
    i.TIM_ClearITPendingBit                  0x080073d4   Section        0  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x0800748c   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08007568   Section        0  stm32f4xx_tim.o(i.TIM_GetITStatus)
    i.TIM_SelectOutputTrigger                0x08007670   Section        0  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_TimeBaseInit                       0x08007724   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.TimingDelay_Decrement                  0x08007888   Section        0  main.o(i.TimingDelay_Decrement)
    i.USART1_IRQHandler                      0x0800788c   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART1_Init                            0x08007928   Section        0  usart.o(i.USART1_Init)
    i.USART_ClearITPendingBit                0x08007a08   Section        0  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08007b08   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_DMACmd                           0x08007bc4   Section        0  stm32f4xx_usart.o(i.USART_DMACmd)
    i.USART_DMA_Config                       0x08007c94   Section        0  usart.o(i.USART_DMA_Config)
    USART_DMA_Config                         0x08007c95   Thumb Code   152  usart.o(i.USART_DMA_Config)
    i.USART_GPIO_Config                      0x08007d3c   Section        0  usart.o(i.USART_GPIO_Config)
    USART_GPIO_Config                        0x08007d3d   Thumb Code    94  usart.o(i.USART_GPIO_Config)
    i.USART_GetFlagStatus                    0x08007da0   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08007ea8   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x0800801c   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x0800817c   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_Module_SendData                  0x080083a8   Section        0  usart.o(i.USART_Module_SendData)
    i.USART_NVIC_Config                      0x080083da   Section        0  usart.o(i.USART_NVIC_Config)
    USART_NVIC_Config                        0x080083db   Thumb Code    92  usart.o(i.USART_NVIC_Config)
    i.USART_Printf                           0x08008438   Section        0  usart.o(i.USART_Printf)
    i.USART_ResetStats                       0x0800846c   Section        0  usart.o(i.USART_ResetStats)
    i.USART_SendByte                         0x0800847c   Section        0  usart.o(i.USART_SendByte)
    i.USART_SendData                         0x080084c4   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.USART_SendString                       0x08008570   Section        0  usart.o(i.USART_SendString)
    i.UsageFault_Handler                     0x0800858c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08008590   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080085c0   Section        0  __printf_wp.o(i._is_digit)
    i.assert_failed                          0x080085d0   Section        0  main.o(i.assert_failed)
    i.main                                   0x080085fc   Section        0  main.o(i.main)
    locale$$code                             0x080089e8   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08008a14   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dfltn                              0x08008a40   Section       26  dflt_clz.o(x$fpl$dfltn)
    $v0                                      0x08008a40   Number         0  dflt_clz.o(x$fpl$dfltn)
    x$fpl$dretinf                            0x08008a5a   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08008a5a   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$exception                          0x08008a68   Section      492  except.o(x$fpl$exception)
    $v0                                      0x08008a68   Number         0  except.o(x$fpl$exception)
    x$fpl$f2d                                0x08008c54   Section      122  f2d.o(x$fpl$f2d)
    $v0                                      0x08008c54   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08008cce   Section      242  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08008cce   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08008dc0   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08008dc0   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$funder                             0x08008dca   Section      316  funder_clz.o(x$fpl$funder)
    $v0                                      0x08008dca   Number         0  funder_clz.o(x$fpl$funder)
    x$fpl$ieeestatus                         0x08008f08   Section       32  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x08008f08   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08008f28   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08008f28   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08008f2c   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08008f2c   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08008f30   Section      178  retnan.o(x$fpl$retnan)
    $v0                                      0x08008f30   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$trapveneer                         0x08008fe2   Section      126  trapv.o(x$fpl$trapveneer)
    $v0                                      0x08008fe2   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08009060   Section       40  main.o(.constdata)
    x$fpl$usenofp                            0x08009060   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08009088   Section    32768  dds_wavegen.o(.constdata)
    .constdata                               0x08011088   Section      354  oled_1.o(.constdata)
    s_font_6x8                               0x08011088   Data         354  oled_1.o(.constdata)
    .constdata                               0x080111ea   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080111ea   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080111fc   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x080111fc   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08011204   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08011204   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08011218   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0801122c   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0801122c   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0801123f   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08011254   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08011254   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08011290   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08011308   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0801130c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08011314   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08011320   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08011322   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08011323   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08011324   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08011324   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08011328   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08011330   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08011434   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       20  main.o(.data)
    g_current_gain_level                     0x20000000   Data           1  main.o(.data)
    g_dac_voltage_a                          0x20000004   Data           4  main.o(.data)
    ad7606_counter                           0x2000000c   Data           4  main.o(.data)
    last_status_time                         0x20000010   Data           4  main.o(.data)
    .data                                    0x20000014   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000028   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000028   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000038   Section       12  adc_dma.o(.data)
    last_samples                             0x2000003c   Data           4  adc_dma.o(.data)
    last_time                                0x20000040   Data           4  adc_dma.o(.data)
    .data                                    0x20000044   Section       20  parallel_adc.o(.data)
    s_last_time                              0x20000050   Data           4  parallel_adc.o(.data)
    s_sample_count                           0x20000054   Data           4  parallel_adc.o(.data)
    .data                                    0x20000058   Section       25  systick.o(.data)
    s_delay_counter                          0x2000006c   Data           4  systick.o(.data)
    s_dwt_initialized                        0x20000070   Data           1  systick.o(.data)
    .data                                    0x20000071   Section        2  usart.o(.data)
    .data                                    0x20000073   Section        2  dds_wavegen.o(.data)
    .data                                    0x20000078   Section        8  key.o(.data)
    s_key_states                             0x20000079   Data           3  key.o(.data)
    s_last_scan_time                         0x2000007c   Data           4  key.o(.data)
    .data                                    0x20000080   Section        1  oled_1.o(.data)
    .data                                    0x20000081   Section        1  cd4052.o(.data)
    current_gain_level                       0x20000081   Data           1  cd4052.o(.data)
    .bss                                     0x20000084   Section      272  main.o(.bss)
    debug_buffer                             0x20000084   Data         256  main.o(.bss)
    g_adc7606_data                           0x20000184   Data          16  main.o(.bss)
    .bss                                     0x20000194   Section    16516  adc_dma.o(.bss)
    s_adc_ping_buffer                        0x20000200   Data        8192  adc_dma.o(.bss)
    s_adc_pong_buffer                        0x20002200   Data        8192  adc_dma.o(.bss)
    s_filter_buffer                          0x20004200   Data          12  adc_dma.o(.bss)
    s_oversample_buffer                      0x2000420c   Data          12  adc_dma.o(.bss)
    .bss                                     0x20004218   Section    16472  parallel_adc.o(.bss)
    s_parallel_adc_buffer                    0x20006270   Data        8192  parallel_adc.o(.bss)
    .bss                                     0x20008270   Section       16  systick.o(.bss)
    .bss                                     0x20008280   Section     3684  usart.o(.bss)
    s_tx_buffer                              0x200082e4   Data        2048  usart.o(.bss)
    s_rx_buffer                              0x20008ae4   Data        1024  usart.o(.bss)
    s_printf_buffer                          0x20008ee4   Data         512  usart.o(.bss)
    .bss                                     0x200090e4   Section     1124  dds_wavegen.o(.bss)
    s_dds_output_buffer                      0x20009148   Data        1024  dds_wavegen.o(.bss)
    .bss                                     0x20009548   Section       12  key.o(.bss)
    s_key_debounce_time                      0x20009548   Data          12  key.o(.bss)
    .bss                                     0x20009554   Section     1024  oled_1.o(.bss)
    s_oled_buffer                            0x20009554   Data        1024  oled_1.o(.bss)
    .bss                                     0x20009954   Section       96  libspace.o(.bss)
    HEAP                                     0x200099b8   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200099b8   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20009bb8   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20009bb8   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20009fb8   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_big                              - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000405   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x08000439   Thumb Code    38  __2sprintf.o(.text)
    _printf_pre_padding                      0x08000465   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000491   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_dec                          0x080004b5   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x0800052d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strlen                                   0x080006b5   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy4                          0x080006f3   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080006f3   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080006f3   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800073b   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000757   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000757   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800075b   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800079b   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800079b   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800079b   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800079f   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080007e9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080007eb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080007ed   Thumb Code     2  heapauxi.o(.text)
    _printf_truncate_signed                  0x080007ef   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000801   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000813   Thumb Code    82  _printf_str.o(.text)
    _printf_int_common                       0x08000865   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000917   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x0800093f   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000af1   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000d67   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000d8d   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000d97   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000da9   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000e65   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000ee1   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000f23   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000f3b   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000f51   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000fa7   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000fc3   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000fcf   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __rt_locale                              0x08000fe5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000fed   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_hex_real                      0x08001079   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x08001375   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_cs_common                        0x080013f5   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001409   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001419   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001421   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001435   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001445   Thumb Code     8  _printf_wchar.o(.text)
    _btod_etento                             0x0800144d   Thumb Code   224  bigflt0.o(.text)
    _wcrtomb                                 0x08001531   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08001571   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001571   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001571   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001579   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080015c5   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x080015d5   Thumb Code    18  exit.o(.text)
    __rt_SIGFPE                              0x080015e7   Thumb Code    14  defsig_fpe_outer.o(.text)
    strcmp                                   0x080015f5   Thumb Code   128  strcmpv7m.o(.text)
    _fp_trap                                 0x08001675   Thumb Code    44  _fptrap.o(.text)
    __sig_exit                               0x080016a5   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGFPE_inner                        0x080016b1   Thumb Code    62  defsig_fpe_inner.o(.text)
    _sys_exit                                0x0800175d   Thumb Code     8  sys_exit.o(.text)
    __default_signal_display                 0x08001769   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x0800179b   Thumb Code    14  sys_wrch.o(.text)
    __I$use$semihosting                      0x080017a9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080017a9   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080017ab   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x080017ab   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080017e9   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800182f   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800188f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001bc7   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001ca3   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001ccd   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001cf7   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AD7606_Init                              0x08001f55   Thumb Code   286  ad7606.o(i.AD7606_Init)
    AD7606_IsBusy                            0x08002081   Thumb Code    12  ad7606.o(i.AD7606_IsBusy)
    AD7606_ReadData                          0x08002091   Thumb Code   132  ad7606.o(i.AD7606_ReadData)
    AD7606_Reset                             0x08002121   Thumb Code    40  ad7606.o(i.AD7606_Reset)
    AD7606_StartConversion                   0x080021a5   Thumb Code    52  ad7606.o(i.AD7606_StartConversion)
    AD7606_Test                              0x080021dd   Thumb Code    34  ad7606.o(i.AD7606_Test)
    ADC1_DMA_Init                            0x08002201   Thumb Code   158  adc_dma.o(i.ADC1_DMA_Init)
    ADC_Calibrate                            0x080022b5   Thumb Code   176  adc_dma.o(i.ADC_Calibrate)
    ADC_ClearITPendingBit                    0x08002369   Thumb Code    88  stm32f4xx_adc.o(i.ADC_ClearITPendingBit)
    ADC_Cmd                                  0x080023e5   Thumb Code    76  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x08002455   Thumb Code   342  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_DMACmd                               0x08002669   Thumb Code    76  stm32f4xx_adc.o(i.ADC_DMACmd)
    ADC_DMARequestAfterLastTransferCmd       0x080026d9   Thumb Code    76  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    ADC_GetITStatus                          0x0800280d   Thumb Code   122  stm32f4xx_adc.o(i.ADC_GetITStatus)
    ADC_GetStats                             0x080028ad   Thumb Code    98  adc_dma.o(i.ADC_GetStats)
    ADC_IRQHandler                           0x08002921   Thumb Code    64  adc_dma.o(i.ADC_IRQHandler)
    ADC_Init                                 0x0800296d   Thumb Code   402  stm32f4xx_adc.o(i.ADC_Init)
    ADC_ProcessData                          0x08002b69   Thumb Code   154  adc_dma.o(i.ADC_ProcessData)
    ADC_RegularChannelConfig                 0x08002c15   Thumb Code   408  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetStats                           0x08002dd1   Thumb Code    36  adc_dma.o(i.ADC_ResetStats)
    ADC_SoftwareStartConv                    0x08002df9   Thumb Code    44  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    ADC_Start_Acquisition                    0x08002e49   Thumb Code    64  adc_dma.o(i.ADC_Start_Acquisition)
    BSP_Init                                 0x08002f59   Thumb Code    52  bsp.o(i.BSP_Init)
    BusFault_Handler                         0x08002f8d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    CD4052_Init                              0x08002f91   Thumb Code    46  cd4052.o(i.CD4052_Init)
    CD4052_SetGain                           0x08002fc5   Thumb Code   106  cd4052.o(i.CD4052_SetGain)
    DAC8552_Init                             0x08003051   Thumb Code   206  dac8552.o(i.DAC8552_Init)
    DAC8552_SetVoltage                       0x08003179   Thumb Code   100  dac8552.o(i.DAC8552_SetVoltage)
    DAC8552_Test                             0x080031e5   Thumb Code    48  dac8552.o(i.DAC8552_Test)
    DAC8552_Write                            0x08003215   Thumb Code   140  dac8552.o(i.DAC8552_Write)
    DAC_Cmd                                  0x080032a5   Thumb Code    76  stm32f4xx_dac.o(i.DAC_Cmd)
    DAC_DMACmd                               0x0800330d   Thumb Code    80  stm32f4xx_dac.o(i.DAC_DMACmd)
    DAC_Init                                 0x08003379   Thumb Code   360  stm32f4xx_dac.o(i.DAC_Init)
    DDS_GetStats                             0x08003695   Thumb Code   114  dds_wavegen.o(i.DDS_GetStats)
    DDS_Init                                 0x08003715   Thumb Code   170  dds_wavegen.o(i.DDS_Init)
    DDS_ResetStats                           0x080038c5   Thumb Code    24  dds_wavegen.o(i.DDS_ResetStats)
    DDS_SetFrequency                         0x080038e1   Thumb Code    40  dds_wavegen.o(i.DDS_SetFrequency)
    DDS_SetWaveType                          0x08003911   Thumb Code    44  dds_wavegen.o(i.DDS_SetWaveType)
    DDS_Start                                0x08003941   Thumb Code    54  dds_wavegen.o(i.DDS_Start)
    DMA1_Stream5_IRQHandler                  0x080039f5   Thumb Code   108  dds_wavegen.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x08003a79   Thumb Code   120  adc_dma.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08003b0d   Thumb Code    92  usart.o(i.DMA2_Stream2_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x08003b7d   Thumb Code    80  usart.o(i.DMA2_Stream7_IRQHandler)
    DMA_ClearITPendingBit                    0x08003be1   Thumb Code   216  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x08003ce1   Thumb Code   182  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x08003db9   Thumb Code   462  stm32f4xx_dma.o(i.DMA_DeInit)
    DMA_DoubleBufferModeCmd                  0x08003fb5   Thumb Code   182  stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd)
    DMA_DoubleBufferModeConfig               0x0800408d   Thumb Code   188  stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig)
    DMA_GetCurrDataCounter                   0x08004169   Thumb Code   146  stm32f4xx_dma.o(i.DMA_GetCurrDataCounter)
    DMA_GetITStatus                          0x0800421d   Thumb Code   498  stm32f4xx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x080044c9   Thumb Code   238  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x080045d9   Thumb Code   658  stm32f4xx_dma.o(i.DMA_Init)
    DMA_SetCurrDataCounter                   0x08004891   Thumb Code   146  stm32f4xx_dma.o(i.DMA_SetCurrDataCounter)
    DWT_Init                                 0x08004945   Thumb Code    78  systick.o(i.DWT_Init)
    DebugMon_Handler                         0x080049a1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080049a5   Thumb Code    40  systick.o(i.Delay_ms)
    EXTI0_IRQHandler                         0x080049d1   Thumb Code     8  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI0_IRQHandler_Internal                0x080049d9   Thumb Code    72  parallel_adc.o(i.EXTI0_IRQHandler_Internal)
    EXTI_ClearITPendingBit                   0x08004a31   Thumb Code    30  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08004a71   Thumb Code   164  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08004b35   Thumb Code   236  stm32f4xx_exti.o(i.EXTI_Init)
    GPIO_Init                                0x08004c41   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08004de9   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08004f91   Thumb Code   194  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x0800509d   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800514d   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_SetBits)
    G_Module_Init                            0x080051fd   Thumb Code   106  main.o(i.G_Module_Init)
    G_Module_Test                            0x0800537d   Thumb Code   158  main.o(i.G_Module_Test)
    HardFault_Handler                        0x08005539   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    I2C_CheckEvent                           0x0800553d   Thumb Code   246  stm32f4xx_i2c.o(i.I2C_CheckEvent)
    I2C_Cmd                                  0x08005679   Thumb Code    78  stm32f4xx_i2c.o(i.I2C_Cmd)
    I2C_DeInit                               0x080056ed   Thumb Code   110  stm32f4xx_i2c.o(i.I2C_DeInit)
    I2C_GenerateSTART                        0x08005781   Thumb Code    78  stm32f4xx_i2c.o(i.I2C_GenerateSTART)
    I2C_GenerateSTOP                         0x080057f5   Thumb Code    78  stm32f4xx_i2c.o(i.I2C_GenerateSTOP)
    I2C_GetFlagStatus                        0x08005869   Thumb Code   238  stm32f4xx_i2c.o(i.I2C_GetFlagStatus)
    I2C_Init                                 0x08005995   Thumb Code   388  stm32f4xx_i2c.o(i.I2C_Init)
    I2C_Send7bitAddress                      0x08005b4d   Thumb Code    72  stm32f4xx_i2c.o(i.I2C_Send7bitAddress)
    I2C_SendData                             0x08005bb9   Thumb Code    40  stm32f4xx_i2c.o(i.I2C_SendData)
    Key_Init                                 0x08005c31   Thumb Code    48  key.o(i.Key_Init)
    Key_Scan                                 0x08005cad   Thumb Code   136  key.o(i.Key_Scan)
    MemManage_Handler                        0x08005d45   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    Module_Init_Demo                         0x08005d49   Thumb Code   278  main.o(i.Module_Init_Demo)
    Module_Test_Demo                         0x080060ad   Thumb Code    88  main.o(i.Module_Test_Demo)
    NMI_Handler                              0x080061c1   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x080061c5   Thumb Code   164  misc.o(i.NVIC_Init)
    OLED_Clear                               0x080062ad   Thumb Code    30  oled_1.o(i.OLED_Clear)
    OLED_Init                                0x08006531   Thumb Code   176  oled_1.o(i.OLED_Init)
    OLED_Refresh                             0x080065e5   Thumb Code   110  oled_1.o(i.OLED_Refresh)
    OLED_ShowChar                            0x0800665d   Thumb Code   136  oled_1.o(i.OLED_ShowChar)
    OLED_ShowString                          0x080066f1   Thumb Code    86  oled_1.o(i.OLED_ShowString)
    ParallelADC_ClearBuffer                  0x080067d5   Thumb Code    24  parallel_adc.o(i.ParallelADC_ClearBuffer)
    ParallelADC_Init                         0x080068a5   Thumb Code    74  parallel_adc.o(i.ParallelADC_Init)
    ParallelADC_ReadSingle                   0x0800691d   Thumb Code    20  parallel_adc.o(i.ParallelADC_ReadSingle)
    ParallelADC_ResetStats                   0x08006931   Thumb Code    38  parallel_adc.o(i.ParallelADC_ResetStats)
    ParallelADC_Start                        0x08006965   Thumb Code    48  parallel_adc.o(i.ParallelADC_Start)
    PendSV_Handler                           0x080069a1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x080069a5   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08006a0d   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x08006a71   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x08006ad5   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08006b3d   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SPI_Cmd                                  0x08006c35   Thumb Code    96  stm32f4xx_spi.o(i.SPI_Cmd)
    SPI_I2S_GetFlagStatus                    0x08006cc5   Thumb Code   134  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    SPI_I2S_ReceiveData                      0x08006d81   Thumb Code    68  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    SPI_I2S_SendData                         0x08006df9   Thumb Code    70  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    SPI_Init                                 0x08006e75   Thumb Code   366  stm32f4xx_spi.o(i.SPI_Init)
    SVC_Handler                              0x08007015   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SYSCFG_EXTILineConfig                    0x08007019   Thumb Code   188  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    SysTick_GetTick                          0x080071e1   Thumb Code     6  systick.o(i.SysTick_GetTick)
    SysTick_GetUptime_ms                     0x080071ed   Thumb Code     6  systick.o(i.SysTick_GetUptime_ms)
    SysTick_Handler                          0x080071f9   Thumb Code    22  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Handler_Internal                 0x08007215   Thumb Code    42  systick.o(i.SysTick_Handler_Internal)
    SysTick_Init                             0x0800724d   Thumb Code   114  systick.o(i.SysTick_Init)
    SysTick_ResetStats                       0x080072c9   Thumb Code    22  systick.o(i.SysTick_ResetStats)
    SystemInit                               0x08007349   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x080073b1   Thumb Code    28  dds_wavegen.o(i.TIM6_DAC_IRQHandler)
    TIM_ClearITPendingBit                    0x080073d5   Thumb Code   108  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x0800748d   Thumb Code   144  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08007569   Thumb Code   186  stm32f4xx_tim.o(i.TIM_GetITStatus)
    TIM_SelectOutputTrigger                  0x08007671   Thumb Code   126  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    TIM_TimeBaseInit                         0x08007725   Thumb Code   278  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    TimingDelay_Decrement                    0x08007889   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    USART1_IRQHandler                        0x0800788d   Thumb Code   138  usart.o(i.USART1_IRQHandler)
    USART1_Init                              0x08007929   Thumb Code   200  usart.o(i.USART1_Init)
    USART_ClearITPendingBit                  0x08007a09   Thumb Code   188  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08007b09   Thumb Code   120  stm32f4xx_usart.o(i.USART_Cmd)
    USART_DMACmd                             0x08007bc5   Thumb Code   138  stm32f4xx_usart.o(i.USART_DMACmd)
    USART_GetFlagStatus                      0x08007da1   Thumb Code   194  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08007ea9   Thumb Code   302  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x0800801d   Thumb Code   284  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x0800817d   Thumb Code   482  stm32f4xx_usart.o(i.USART_Init)
    USART_Module_SendData                    0x080083a9   Thumb Code    50  usart.o(i.USART_Module_SendData)
    USART_Printf                             0x08008439   Thumb Code    46  usart.o(i.USART_Printf)
    USART_ResetStats                         0x0800846d   Thumb Code    12  usart.o(i.USART_ResetStats)
    USART_SendByte                           0x0800847d   Thumb Code    64  usart.o(i.USART_SendByte)
    USART_SendData                           0x080084c5   Thumb Code   104  stm32f4xx_usart.o(i.USART_SendData)
    USART_SendString                         0x08008571   Thumb Code    28  usart.o(i.USART_SendString)
    UsageFault_Handler                       0x0800858d   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08008591   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080085c1   Thumb Code    14  __printf_wp.o(i._is_digit)
    assert_failed                            0x080085d1   Thumb Code    18  main.o(i.assert_failed)
    main                                     0x080085fd   Thumb Code   630  main.o(i.main)
    _get_lc_numeric                          0x080089e9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08008a15   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __dflt_normalise                         0x08008a41   Thumb Code    26  dflt_clz.o(x$fpl$dfltn)
    __fpl_dretinf                            0x08008a5b   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __fpl_exception                          0x08008a69   Thumb Code   454  except.o(x$fpl$exception)
    __aeabi_f2d                              0x08008c55   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08008c55   Thumb Code   122  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08008ccf   Thumb Code   242  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08008dc1   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08008dc9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08008dc9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __funder                                 0x08008dcb   Thumb Code   254  funder_clz.o(x$fpl$funder)
    __funder_d                               0x08008ec9   Thumb Code    62  funder_clz.o(x$fpl$funder)
    __ieee_status                            0x08008f09   Thumb Code    28  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08008f29   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08008f2d   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08008f31   Thumb Code   178  retnan.o(x$fpl$retnan)
    _fp_trapveneer                           0x08008fe3   Thumb Code    36  trapv.o(x$fpl$trapveneer)
    __fpl_cmpreturn                          0x08009007   Thumb Code    90  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08009060   Number         0  usenofp.o(x$fpl$usenofp)
    g_sin_wave_table                         0x08009088   Data        8192  dds_wavegen.o(.constdata)
    g_square_wave_table                      0x0800b088   Data        8192  dds_wavegen.o(.constdata)
    g_triangle_wave_table                    0x0800d088   Data        8192  dds_wavegen.o(.constdata)
    g_sawtooth_wave_table                    0x0800f088   Data        8192  dds_wavegen.o(.constdata)
    Region$$Table$$Base                      0x080112e8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08011308   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08011331   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000008   Data           4  main.o(.data)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000018   Data          16  system_stm32f4xx.o(.data)
    g_adc_conversion_complete                0x20000038   Data           1  adc_dma.o(.data)
    g_adc_dma_half_complete                  0x20000039   Data           1  adc_dma.o(.data)
    g_adc_dma_full_complete                  0x2000003a   Data           1  adc_dma.o(.data)
    g_parallel_adc_data_ready                0x20000044   Data           1  parallel_adc.o(.data)
    g_parallel_adc_overflow                  0x20000045   Data           1  parallel_adc.o(.data)
    g_adc_buffer_index                       0x20000048   Data           4  parallel_adc.o(.data)
    g_adc_capture_finished                   0x2000004c   Data           1  parallel_adc.o(.data)
    g_systick_counter                        0x20000058   Data           4  systick.o(.data)
    g_system_uptime_ms                       0x2000005c   Data           4  systick.o(.data)
    g_systick_cal                            0x20000060   Data          12  systick.o(.data)
    g_usart_tx_complete_flag                 0x20000071   Data           1  usart.o(.data)
    g_usart_rx_complete_flag                 0x20000072   Data           1  usart.o(.data)
    g_dds_update_complete                    0x20000073   Data           1  dds_wavegen.o(.data)
    g_dds_dma_complete                       0x20000074   Data           1  dds_wavegen.o(.data)
    g_key_initialized                        0x20000078   Data           1  key.o(.data)
    g_oled_initialized                       0x20000080   Data           1  oled_1.o(.data)
    g_adc1_handle                            0x20000194   Data         108  adc_dma.o(.bss)
    g_parallel_adc_handle                    0x20004218   Data          88  parallel_adc.o(.bss)
    g_adc_buffer                             0x20004270   Data        8192  parallel_adc.o(.bss)
    g_systick_stats                          0x20008270   Data          16  systick.o(.bss)
    g_usart1_handle                          0x20008280   Data         100  usart.o(.bss)
    g_dds_handle                             0x200090e4   Data         100  dds_wavegen.o(.bss)
    __libspace_start                         0x20009954   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200099b4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000114b8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00011434, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         7043  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         7368    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         7370    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         7372    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         7030    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         7104    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         7106    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         7029    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         7109    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         7110    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         7111    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         7116    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         7108    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         7027    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         7028    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         7107    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         7105    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         7113    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         7114    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         7115    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         7120    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         7121    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         7117    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         7102    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         7103    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         7118    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         7119    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         7112    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         7232    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         7233    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         7236    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         7239    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         7241    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         7243    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         7244    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         7246    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         7247    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         7248    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         7250    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         7251    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7252    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7254    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7256    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7258    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7260    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7262    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7264    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7266    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7270    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7272    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7274    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         7276    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         7277    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         7333    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7351    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7353    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7356    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7359    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7361    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         7364    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         7365    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         7049    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         7140    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         7152    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         7142    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         7143    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         7145    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         7146    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         7282    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         7302    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         7303    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         7304    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x08000314   0x08000314   0x000000ee   Code   RO         6993    .text               c_w.l(lludivv7m.o)
    0x08000402   0x08000402   0x00000002   PAD
    0x08000404   0x08000404   0x00000034   Code   RO         6995    .text               c_w.l(vsnprintf.o)
    0x08000438   0x08000438   0x0000002c   Code   RO         6997    .text               c_w.l(__2sprintf.o)
    0x08000464   0x08000464   0x0000004e   Code   RO         7003    .text               c_w.l(_printf_pad.o)
    0x080004b2   0x080004b2   0x00000002   PAD
    0x080004b4   0x080004b4   0x00000078   Code   RO         7005    .text               c_w.l(_printf_dec.o)
    0x0800052c   0x0800052c   0x00000188   Code   RO         7024    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080006b4   0x080006b4   0x0000003e   Code   RO         7031    .text               c_w.l(strlen.o)
    0x080006f2   0x080006f2   0x00000064   Code   RO         7035    .text               c_w.l(rt_memcpy_w.o)
    0x08000756   0x08000756   0x00000044   Code   RO         7037    .text               c_w.l(rt_memclr.o)
    0x0800079a   0x0800079a   0x0000004e   Code   RO         7039    .text               c_w.l(rt_memclr_w.o)
    0x080007e8   0x080007e8   0x00000006   Code   RO         7041    .text               c_w.l(heapauxi.o)
    0x080007ee   0x080007ee   0x00000024   Code   RO         7054    .text               c_w.l(_printf_truncate.o)
    0x08000812   0x08000812   0x00000052   Code   RO         7056    .text               c_w.l(_printf_str.o)
    0x08000864   0x08000864   0x000000b2   Code   RO         7058    .text               c_w.l(_printf_intcommon.o)
    0x08000916   0x08000916   0x00000028   Code   RO         7060    .text               c_w.l(_printf_charcount.o)
    0x0800093e   0x0800093e   0x0000041e   Code   RO         7062    .text               c_w.l(_printf_fp_dec.o)
    0x08000d5c   0x08000d5c   0x00000030   Code   RO         7064    .text               c_w.l(_printf_char_common.o)
    0x08000d8c   0x08000d8c   0x0000000a   Code   RO         7066    .text               c_w.l(_sputc.o)
    0x08000d96   0x08000d96   0x00000010   Code   RO         7068    .text               c_w.l(_snputc.o)
    0x08000da6   0x08000da6   0x00000002   PAD
    0x08000da8   0x08000da8   0x000000bc   Code   RO         7070    .text               c_w.l(_printf_wctomb.o)
    0x08000e64   0x08000e64   0x0000007c   Code   RO         7073    .text               c_w.l(_printf_longlong_dec.o)
    0x08000ee0   0x08000ee0   0x00000070   Code   RO         7079    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000f50   0x08000f50   0x00000094   Code   RO         7099    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000fe4   0x08000fe4   0x00000008   Code   RO         7159    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000fec   0x08000fec   0x0000008a   Code   RO         7161    .text               c_w.l(lludiv10.o)
    0x08001076   0x08001076   0x00000002   PAD
    0x08001078   0x08001078   0x000002fc   Code   RO         7163    .text               c_w.l(_printf_fp_hex.o)
    0x08001374   0x08001374   0x00000080   Code   RO         7166    .text               c_w.l(_printf_fp_infnan.o)
    0x080013f4   0x080013f4   0x0000002c   Code   RO         7170    .text               c_w.l(_printf_char.o)
    0x08001420   0x08001420   0x0000002c   Code   RO         7172    .text               c_w.l(_printf_wchar.o)
    0x0800144c   0x0800144c   0x000000e4   Code   RO         7174    .text               c_w.l(bigflt0.o)
    0x08001530   0x08001530   0x00000040   Code   RO         7199    .text               c_w.l(_wcrtomb.o)
    0x08001570   0x08001570   0x00000008   Code   RO         7212    .text               c_w.l(libspace.o)
    0x08001578   0x08001578   0x0000004a   Code   RO         7215    .text               c_w.l(sys_stackheap_outer.o)
    0x080015c2   0x080015c2   0x00000002   PAD
    0x080015c4   0x080015c4   0x00000010   Code   RO         7217    .text               c_w.l(rt_ctype_table.o)
    0x080015d4   0x080015d4   0x00000012   Code   RO         7219    .text               c_w.l(exit.o)
    0x080015e6   0x080015e6   0x0000000e   Code   RO         7221    .text               c_w.l(defsig_fpe_outer.o)
    0x080015f4   0x080015f4   0x00000080   Code   RO         7225    .text               c_w.l(strcmpv7m.o)
    0x08001674   0x08001674   0x00000030   Code   RO         7286    .text               c_w.l(_fptrap.o)
    0x080016a4   0x080016a4   0x0000000a   Code   RO         7291    .text               c_w.l(defsig_exit.o)
    0x080016ae   0x080016ae   0x00000002   PAD
    0x080016b0   0x080016b0   0x000000ac   Code   RO         7293    .text               c_w.l(defsig_fpe_inner.o)
    0x0800175c   0x0800175c   0x0000000c   Code   RO         7299    .text               c_w.l(sys_exit.o)
    0x08001768   0x08001768   0x00000032   Code   RO         7310    .text               c_w.l(defsig_general.o)
    0x0800179a   0x0800179a   0x0000000e   Code   RO         7316    .text               c_w.l(sys_wrch.o)
    0x080017a8   0x080017a8   0x00000002   Code   RO         7320    .text               c_w.l(use_no_semi.o)
    0x080017aa   0x080017aa   0x00000000   Code   RO         7322    .text               c_w.l(indicate_semi.o)
    0x080017aa   0x080017aa   0x0000003e   Code   RO         7177    CL$$btod_d2e        c_w.l(btod.o)
    0x080017e8   0x080017e8   0x00000046   Code   RO         7179    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800182e   0x0800182e   0x00000060   Code   RO         7178    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800188e   0x0800188e   0x00000338   Code   RO         7187    CL$$btod_div_common  c_w.l(btod.o)
    0x08001bc6   0x08001bc6   0x000000dc   Code   RO         7184    CL$$btod_e2e        c_w.l(btod.o)
    0x08001ca2   0x08001ca2   0x0000002a   Code   RO         7181    CL$$btod_ediv       c_w.l(btod.o)
    0x08001ccc   0x08001ccc   0x0000002a   Code   RO         7180    CL$$btod_emul       c_w.l(btod.o)
    0x08001cf6   0x08001cf6   0x00000244   Code   RO         7186    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001f3a   0x08001f3a   0x00000018   Code   RO         6626    i.AD7606_Delay_us   ad7606.o
    0x08001f52   0x08001f52   0x00000002   PAD
    0x08001f54   0x08001f54   0x0000012c   Code   RO         6627    i.AD7606_Init       ad7606.o
    0x08002080   0x08002080   0x00000010   Code   RO         6628    i.AD7606_IsBusy     ad7606.o
    0x08002090   0x08002090   0x00000090   Code   RO         6629    i.AD7606_ReadData   ad7606.o
    0x08002120   0x08002120   0x0000002c   Code   RO         6630    i.AD7606_Reset      ad7606.o
    0x0800214c   0x0800214c   0x00000058   Code   RO         6631    i.AD7606_SPI_ReadWord  ad7606.o
    0x080021a4   0x080021a4   0x00000038   Code   RO         6632    i.AD7606_StartConversion  ad7606.o
    0x080021dc   0x080021dc   0x00000022   Code   RO         6633    i.AD7606_Test       ad7606.o
    0x080021fe   0x080021fe   0x00000002   PAD
    0x08002200   0x08002200   0x000000b4   Code   RO         5515    i.ADC1_DMA_Init     adc_dma.o
    0x080022b4   0x080022b4   0x000000b4   Code   RO         5517    i.ADC_Calibrate     adc_dma.o
    0x08002368   0x08002368   0x0000007c   Code   RO          485    i.ADC_ClearITPendingBit  stm32f4xx_adc.o
    0x080023e4   0x080023e4   0x00000070   Code   RO          486    i.ADC_Cmd           stm32f4xx_adc.o
    0x08002454   0x08002454   0x0000017c   Code   RO          487    i.ADC_CommonInit    stm32f4xx_adc.o
    0x080025d0   0x080025d0   0x00000098   Code   RO         5518    i.ADC_Config        adc_dma.o
    0x08002668   0x08002668   0x00000070   Code   RO          490    i.ADC_DMACmd        stm32f4xx_adc.o
    0x080026d8   0x080026d8   0x00000070   Code   RO          491    i.ADC_DMARequestAfterLastTransferCmd  stm32f4xx_adc.o
    0x08002748   0x08002748   0x0000009c   Code   RO         5519    i.ADC_DMA_Config    adc_dma.o
    0x080027e4   0x080027e4   0x00000028   Code   RO         5520    i.ADC_GPIO_Config   adc_dma.o
    0x0800280c   0x0800280c   0x000000a0   Code   RO          500    i.ADC_GetITStatus   stm32f4xx_adc.o
    0x080028ac   0x080028ac   0x00000074   Code   RO         5524    i.ADC_GetStats      adc_dma.o
    0x08002920   0x08002920   0x0000004c   Code   RO         5525    i.ADC_IRQHandler    adc_dma.o
    0x0800296c   0x0800296c   0x000001c0   Code   RO          506    i.ADC_Init          stm32f4xx_adc.o
    0x08002b2c   0x08002b2c   0x0000003a   Code   RO         5526    i.ADC_NVIC_Config   adc_dma.o
    0x08002b66   0x08002b66   0x00000002   PAD
    0x08002b68   0x08002b68   0x000000ac   Code   RO         5527    i.ADC_ProcessData   adc_dma.o
    0x08002c14   0x08002c14   0x000001bc   Code   RO          511    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08002dd0   0x08002dd0   0x00000028   Code   RO         5528    i.ADC_ResetStats    adc_dma.o
    0x08002df8   0x08002df8   0x00000050   Code   RO          513    i.ADC_SoftwareStartConv  stm32f4xx_adc.o
    0x08002e48   0x08002e48   0x00000058   Code   RO         5530    i.ADC_Start_Acquisition  adc_dma.o
    0x08002ea0   0x08002ea0   0x00000044   Code   RO         5532    i.ADC_Timer_Config  adc_dma.o
    0x08002ee4   0x08002ee4   0x00000074   Code   RO         5533    i.ADC_UpdateStats   adc_dma.o
    0x08002f58   0x08002f58   0x00000034   Code   RO          379    i.BSP_Init          bsp.o
    0x08002f8c   0x08002f8c   0x00000004   Code   RO          249    i.BusFault_Handler  stm32f4xx_it.o
    0x08002f90   0x08002f90   0x00000034   Code   RO         6694    i.CD4052_Init       cd4052.o
    0x08002fc4   0x08002fc4   0x00000074   Code   RO         6695    i.CD4052_SetGain    cd4052.o
    0x08003038   0x08003038   0x00000018   Code   RO         6571    i.DAC8552_Delay_us  dac8552.o
    0x08003050   0x08003050   0x000000d8   Code   RO         6572    i.DAC8552_Init      dac8552.o
    0x08003128   0x08003128   0x00000050   Code   RO         6573    i.DAC8552_SPI_SendByte  dac8552.o
    0x08003178   0x08003178   0x0000006c   Code   RO         6574    i.DAC8552_SetVoltage  dac8552.o
    0x080031e4   0x080031e4   0x00000030   Code   RO         6575    i.DAC8552_Test      dac8552.o
    0x08003214   0x08003214   0x00000090   Code   RO         6576    i.DAC8552_Write     dac8552.o
    0x080032a4   0x080032a4   0x00000068   Code   RO         1225    i.DAC_Cmd           stm32f4xx_dac.o
    0x0800330c   0x0800330c   0x0000006c   Code   RO         1226    i.DAC_DMACmd        stm32f4xx_dac.o
    0x08003378   0x08003378   0x00000184   Code   RO         1233    i.DAC_Init          stm32f4xx_dac.o
    0x080034fc   0x080034fc   0x0000002e   Code   RO         6105    i.DDS_DAC_Config    dds_wavegen.o
    0x0800352a   0x0800352a   0x00000002   PAD
    0x0800352c   0x0800352c   0x0000007c   Code   RO         6106    i.DDS_DMA_Config    dds_wavegen.o
    0x080035a8   0x080035a8   0x00000028   Code   RO         6108    i.DDS_GPIO_Config   dds_wavegen.o
    0x080035d0   0x080035d0   0x00000058   Code   RO         6109    i.DDS_GenerateWaveTable  dds_wavegen.o
    0x08003628   0x08003628   0x0000006c   Code   RO         6111    i.DDS_GetNextSample  dds_wavegen.o
    0x08003694   0x08003694   0x00000080   Code   RO         6112    i.DDS_GetStats      dds_wavegen.o
    0x08003714   0x08003714   0x000000c4   Code   RO         6113    i.DDS_Init          dds_wavegen.o
    0x080037d8   0x080037d8   0x00000040   Code   RO         6114    i.DDS_LinearInterpolation  dds_wavegen.o
    0x08003818   0x08003818   0x0000003c   Code   RO         6115    i.DDS_NVIC_Config   dds_wavegen.o
    0x08003854   0x08003854   0x00000070   Code   RO         6116    i.DDS_ProcessModulation  dds_wavegen.o
    0x080038c4   0x080038c4   0x0000001c   Code   RO         6117    i.DDS_ResetStats    dds_wavegen.o
    0x080038e0   0x080038e0   0x00000030   Code   RO         6120    i.DDS_SetFrequency  dds_wavegen.o
    0x08003910   0x08003910   0x00000030   Code   RO         6123    i.DDS_SetWaveType   dds_wavegen.o
    0x08003940   0x08003940   0x0000004c   Code   RO         6124    i.DDS_Start         dds_wavegen.o
    0x0800398c   0x0800398c   0x00000048   Code   RO         6126    i.DDS_TIM_Config    dds_wavegen.o
    0x080039d4   0x080039d4   0x00000020   Code   RO         6127    i.DDS_UpdateFrequencyWord  dds_wavegen.o
    0x080039f4   0x080039f4   0x00000084   Code   RO         6128    i.DMA1_Stream5_IRQHandler  dds_wavegen.o
    0x08003a78   0x08003a78   0x00000094   Code   RO         5534    i.DMA2_Stream0_IRQHandler  adc_dma.o
    0x08003b0c   0x08003b0c   0x00000070   Code   RO         5926    i.DMA2_Stream2_IRQHandler  usart.o
    0x08003b7c   0x08003b7c   0x00000064   Code   RO         5927    i.DMA2_Stream7_IRQHandler  usart.o
    0x08003be0   0x08003be0   0x00000100   Code   RO         1533    i.DMA_ClearITPendingBit  stm32f4xx_dma.o
    0x08003ce0   0x08003ce0   0x000000d8   Code   RO         1534    i.DMA_Cmd           stm32f4xx_dma.o
    0x08003db8   0x08003db8   0x000001fc   Code   RO         1535    i.DMA_DeInit        stm32f4xx_dma.o
    0x08003fb4   0x08003fb4   0x000000d8   Code   RO         1536    i.DMA_DoubleBufferModeCmd  stm32f4xx_dma.o
    0x0800408c   0x0800408c   0x000000dc   Code   RO         1537    i.DMA_DoubleBufferModeConfig  stm32f4xx_dma.o
    0x08004168   0x08004168   0x000000b4   Code   RO         1540    i.DMA_GetCurrDataCounter  stm32f4xx_dma.o
    0x0800421c   0x0800421c   0x000002ac   Code   RO         1544    i.DMA_GetITStatus   stm32f4xx_dma.o
    0x080044c8   0x080044c8   0x00000110   Code   RO         1545    i.DMA_ITConfig      stm32f4xx_dma.o
    0x080045d8   0x080045d8   0x000002b8   Code   RO         1546    i.DMA_Init          stm32f4xx_dma.o
    0x08004890   0x08004890   0x000000b4   Code   RO         1549    i.DMA_SetCurrDataCounter  stm32f4xx_dma.o
    0x08004944   0x08004944   0x0000005c   Code   RO         5792    i.DWT_Init          systick.o
    0x080049a0   0x080049a0   0x00000002   Code   RO          250    i.DebugMon_Handler  stm32f4xx_it.o
    0x080049a2   0x080049a2   0x00000002   PAD
    0x080049a4   0x080049a4   0x0000002c   Code   RO         5793    i.Delay_ms          systick.o
    0x080049d0   0x080049d0   0x00000008   Code   RO          251    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x080049d8   0x080049d8   0x00000058   Code   RO         5660    i.EXTI0_IRQHandler_Internal  parallel_adc.o
    0x08004a30   0x08004a30   0x00000040   Code   RO         1824    i.EXTI_ClearITPendingBit  stm32f4xx_exti.o
    0x08004a70   0x08004a70   0x000000c4   Code   RO         1828    i.EXTI_GetITStatus  stm32f4xx_exti.o
    0x08004b34   0x08004b34   0x0000010c   Code   RO         1829    i.EXTI_Init         stm32f4xx_exti.o
    0x08004c40   0x08004c40   0x000001a8   Code   RO         2346    i.GPIO_Init         stm32f4xx_gpio.o
    0x08004de8   0x08004de8   0x000001a8   Code   RO         2347    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08004f90   0x08004f90   0x0000010c   Code   RO         2350    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x0800509c   0x0800509c   0x000000b0   Code   RO         2353    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x0800514c   0x0800514c   0x000000b0   Code   RO         2354    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x080051fc   0x080051fc   0x00000180   Code   RO           14    i.G_Module_Init     main.o
    0x0800537c   0x0800537c   0x000001bc   Code   RO           15    i.G_Module_Test     main.o
    0x08005538   0x08005538   0x00000004   Code   RO          252    i.HardFault_Handler  stm32f4xx_it.o
    0x0800553c   0x0800553c   0x0000013c   Code   RO         2643    i.I2C_CheckEvent    stm32f4xx_i2c.o
    0x08005678   0x08005678   0x00000074   Code   RO         2646    i.I2C_Cmd           stm32f4xx_i2c.o
    0x080056ec   0x080056ec   0x00000094   Code   RO         2649    i.I2C_DeInit        stm32f4xx_i2c.o
    0x08005780   0x08005780   0x00000074   Code   RO         2654    i.I2C_GenerateSTART  stm32f4xx_i2c.o
    0x080057f4   0x080057f4   0x00000074   Code   RO         2655    i.I2C_GenerateSTOP  stm32f4xx_i2c.o
    0x08005868   0x08005868   0x0000012c   Code   RO         2656    i.I2C_GetFlagStatus  stm32f4xx_i2c.o
    0x08005994   0x08005994   0x000001b8   Code   RO         2661    i.I2C_Init          stm32f4xx_i2c.o
    0x08005b4c   0x08005b4c   0x0000006c   Code   RO         2668    i.I2C_Send7bitAddress  stm32f4xx_i2c.o
    0x08005bb8   0x08005bb8   0x0000004c   Code   RO         2669    i.I2C_SendData      stm32f4xx_i2c.o
    0x08005c04   0x08005c04   0x0000002c   Code   RO         6282    i.Key_GPIO_Config   key.o
    0x08005c30   0x08005c30   0x00000040   Code   RO         6284    i.Key_Init          key.o
    0x08005c70   0x08005c70   0x0000003c   Code   RO         6287    i.Key_ReadRaw       key.o
    0x08005cac   0x08005cac   0x00000098   Code   RO         6288    i.Key_Scan          key.o
    0x08005d44   0x08005d44   0x00000004   Code   RO          253    i.MemManage_Handler  stm32f4xx_it.o
    0x08005d48   0x08005d48   0x00000364   Code   RO           16    i.Module_Init_Demo  main.o
    0x080060ac   0x080060ac   0x00000114   Code   RO           17    i.Module_Test_Demo  main.o
    0x080061c0   0x080061c0   0x00000002   Code   RO          254    i.NMI_Handler       stm32f4xx_it.o
    0x080061c2   0x080061c2   0x00000002   PAD
    0x080061c4   0x080061c4   0x000000c0   Code   RO          406    i.NVIC_Init         misc.o
    0x08006284   0x08006284   0x00000028   Code   RO         5796    i.NVIC_SetPriority  systick.o
    0x080062ac   0x080062ac   0x00000028   Code   RO         6364    i.OLED_Clear        oled_1.o
    0x080062d4   0x080062d4   0x00000094   Code   RO         6368    i.OLED_I2C_Config   oled_1.o
    0x08006368   0x08006368   0x000000ec   Code   RO         6369    i.OLED_I2C_WriteBuffer  oled_1.o
    0x08006454   0x08006454   0x000000dc   Code   RO         6370    i.OLED_I2C_WriteCmd  oled_1.o
    0x08006530   0x08006530   0x000000b4   Code   RO         6372    i.OLED_Init         oled_1.o
    0x080065e4   0x080065e4   0x00000078   Code   RO         6374    i.OLED_Refresh      oled_1.o
    0x0800665c   0x0800665c   0x00000094   Code   RO         6376    i.OLED_ShowChar     oled_1.o
    0x080066f0   0x080066f0   0x0000005c   Code   RO         6379    i.OLED_ShowString   oled_1.o
    0x0800674c   0x0800674c   0x00000028   Code   RO         5661    i.ParallelADC_Buffer_Init  parallel_adc.o
    0x08006774   0x08006774   0x00000060   Code   RO         5662    i.ParallelADC_Buffer_Read  parallel_adc.o
    0x080067d4   0x080067d4   0x0000001c   Code   RO         5664    i.ParallelADC_ClearBuffer  parallel_adc.o
    0x080067f0   0x080067f0   0x00000038   Code   RO         5665    i.ParallelADC_EXTI_Config  parallel_adc.o
    0x08006828   0x08006828   0x0000007c   Code   RO         5666    i.ParallelADC_GPIO_Config  parallel_adc.o
    0x080068a4   0x080068a4   0x00000058   Code   RO         5670    i.ParallelADC_Init  parallel_adc.o
    0x080068fc   0x080068fc   0x00000020   Code   RO         5671    i.ParallelADC_NVIC_Config  parallel_adc.o
    0x0800691c   0x0800691c   0x00000014   Code   RO         5673    i.ParallelADC_ReadSingle  parallel_adc.o
    0x08006930   0x08006930   0x00000034   Code   RO         5674    i.ParallelADC_ResetStats  parallel_adc.o
    0x08006964   0x08006964   0x0000003c   Code   RO         5676    i.ParallelADC_Start  parallel_adc.o
    0x080069a0   0x080069a0   0x00000002   Code   RO          255    i.PendSV_Handler    stm32f4xx_it.o
    0x080069a2   0x080069a2   0x00000002   PAD
    0x080069a4   0x080069a4   0x00000068   Code   RO         3287    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08006a0c   0x08006a0c   0x00000064   Code   RO         3296    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08006a70   0x08006a70   0x00000064   Code   RO         3298    i.RCC_APB1PeriphResetCmd  stm32f4xx_rcc.o
    0x08006ad4   0x08006ad4   0x00000068   Code   RO         3299    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08006b3c   0x08006b3c   0x000000e8   Code   RO         3308    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08006c24   0x08006c24   0x00000010   Code   RO         5928    i.RingBuffer_Init   usart.o
    0x08006c34   0x08006c34   0x00000090   Code   RO         4454    i.SPI_Cmd           stm32f4xx_spi.o
    0x08006cc4   0x08006cc4   0x000000bc   Code   RO         4462    i.SPI_I2S_GetFlagStatus  stm32f4xx_spi.o
    0x08006d80   0x08006d80   0x00000078   Code   RO         4465    i.SPI_I2S_ReceiveData  stm32f4xx_spi.o
    0x08006df8   0x08006df8   0x0000007c   Code   RO         4466    i.SPI_I2S_SendData  stm32f4xx_spi.o
    0x08006e74   0x08006e74   0x000001a0   Code   RO         4467    i.SPI_Init          stm32f4xx_spi.o
    0x08007014   0x08007014   0x00000002   Code   RO          256    i.SVC_Handler       stm32f4xx_it.o
    0x08007016   0x08007016   0x00000002   PAD
    0x08007018   0x08007018   0x000000dc   Code   RO         4622    i.SYSCFG_EXTILineConfig  stm32f4xx_syscfg.o
    0x080070f4   0x080070f4   0x000000ec   Code   RO          338    i.SetSysClock       system_stm32f4xx.o
    0x080071e0   0x080071e0   0x0000000c   Code   RO         5800    i.SysTick_GetTick   systick.o
    0x080071ec   0x080071ec   0x0000000c   Code   RO         5802    i.SysTick_GetUptime_ms  systick.o
    0x080071f8   0x080071f8   0x0000001c   Code   RO          257    i.SysTick_Handler   stm32f4xx_it.o
    0x08007214   0x08007214   0x00000038   Code   RO         5803    i.SysTick_Handler_Internal  systick.o
    0x0800724c   0x0800724c   0x0000007c   Code   RO         5804    i.SysTick_Init      systick.o
    0x080072c8   0x080072c8   0x00000020   Code   RO         5807    i.SysTick_ResetStats  systick.o
    0x080072e8   0x080072e8   0x00000060   Code   RO         5809    i.SysTick_UpdateStats  systick.o
    0x08007348   0x08007348   0x00000068   Code   RO          340    i.SystemInit        system_stm32f4xx.o
    0x080073b0   0x080073b0   0x00000024   Code   RO         6129    i.TIM6_DAC_IRQHandler  dds_wavegen.o
    0x080073d4   0x080073d4   0x000000b8   Code   RO         4693    i.TIM_ClearITPendingBit  stm32f4xx_tim.o
    0x0800748c   0x0800748c   0x000000dc   Code   RO         4698    i.TIM_Cmd           stm32f4xx_tim.o
    0x08007568   0x08007568   0x00000108   Code   RO         4719    i.TIM_GetITStatus   stm32f4xx_tim.o
    0x08007670   0x08007670   0x000000b4   Code   RO         4756    i.TIM_SelectOutputTrigger  stm32f4xx_tim.o
    0x08007724   0x08007724   0x00000164   Code   RO         4770    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x08007888   0x08007888   0x00000002   Code   RO           19    i.TimingDelay_Decrement  main.o
    0x0800788a   0x0800788a   0x00000002   PAD
    0x0800788c   0x0800788c   0x0000009c   Code   RO         5931    i.USART1_IRQHandler  usart.o
    0x08007928   0x08007928   0x000000e0   Code   RO         5932    i.USART1_Init       usart.o
    0x08007a08   0x08007a08   0x00000100   Code   RO         5252    i.USART_ClearITPendingBit  stm32f4xx_usart.o
    0x08007b08   0x08007b08   0x000000bc   Code   RO         5255    i.USART_Cmd         stm32f4xx_usart.o
    0x08007bc4   0x08007bc4   0x000000d0   Code   RO         5256    i.USART_DMACmd      stm32f4xx_usart.o
    0x08007c94   0x08007c94   0x000000a8   Code   RO         5933    i.USART_DMA_Config  usart.o
    0x08007d3c   0x08007d3c   0x00000064   Code   RO         5934    i.USART_GPIO_Config  usart.o
    0x08007da0   0x08007da0   0x00000108   Code   RO         5258    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08007ea8   0x08007ea8   0x00000174   Code   RO         5259    i.USART_GetITStatus  stm32f4xx_usart.o
    0x0800801c   0x0800801c   0x00000160   Code   RO         5261    i.USART_ITConfig    stm32f4xx_usart.o
    0x0800817c   0x0800817c   0x0000022c   Code   RO         5262    i.USART_Init        stm32f4xx_usart.o
    0x080083a8   0x080083a8   0x00000032   Code   RO         5938    i.USART_Module_SendData  usart.o
    0x080083da   0x080083da   0x0000005c   Code   RO         5939    i.USART_NVIC_Config  usart.o
    0x08008436   0x08008436   0x00000002   PAD
    0x08008438   0x08008438   0x00000034   Code   RO         5940    i.USART_Printf      usart.o
    0x0800846c   0x0800846c   0x00000010   Code   RO         5942    i.USART_ResetStats  usart.o
    0x0800847c   0x0800847c   0x00000048   Code   RO         5943    i.USART_SendByte    usart.o
    0x080084c4   0x080084c4   0x000000ac   Code   RO         5272    i.USART_SendData    stm32f4xx_usart.o
    0x08008570   0x08008570   0x0000001c   Code   RO         5946    i.USART_SendString  usart.o
    0x0800858c   0x0800858c   0x00000004   Code   RO          258    i.UsageFault_Handler  stm32f4xx_it.o
    0x08008590   0x08008590   0x00000030   Code   RO         7210    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080085c0   0x080085c0   0x0000000e   Code   RO         7017    i._is_digit         c_w.l(__printf_wp.o)
    0x080085ce   0x080085ce   0x00000002   PAD
    0x080085d0   0x080085d0   0x0000002c   Code   RO           20    i.assert_failed     main.o
    0x080085fc   0x080085fc   0x000003ec   Code   RO           21    i.main              main.o
    0x080089e8   0x080089e8   0x0000002c   Code   RO         7202    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008a14   0x08008a14   0x0000002c   Code   RO         7289    locale$$code        c_w.l(lc_ctype_c.o)
    0x08008a40   0x08008a40   0x0000001a   Code   RO         7124    x$fpl$dfltn         g_wm.l(dflt_clz.o)
    0x08008a5a   0x08008a5a   0x0000000c   Code   RO         7128    x$fpl$dretinf       g_wm.l(dretinf.o)
    0x08008a66   0x08008a66   0x00000002   PAD
    0x08008a68   0x08008a68   0x000001ec   Code   RO         7204    x$fpl$exception     g_wm.l(except.o)
    0x08008c54   0x08008c54   0x0000007a   Code   RO         7045    x$fpl$f2d           g_wm.l(f2d.o)
    0x08008cce   0x08008cce   0x000000f2   Code   RO         7130    x$fpl$fnaninf       g_wm.l(fnaninf.o)
    0x08008dc0   0x08008dc0   0x0000000a   Code   RO         7297    x$fpl$fpinit        g_wm.l(fpinit.o)
    0x08008dca   0x08008dca   0x0000013c   Code   RO         7206    x$fpl$funder        g_wm.l(funder_clz.o)
    0x08008f06   0x08008f06   0x00000002   PAD
    0x08008f08   0x08008f08   0x00000020   Code   RO         7208    x$fpl$ieeestatus    g_wm.l(istatus.o)
    0x08008f28   0x08008f28   0x00000004   Code   RO         7047    x$fpl$printf1       g_wm.l(printf1.o)
    0x08008f2c   0x08008f2c   0x00000004   Code   RO         7132    x$fpl$printf2       g_wm.l(printf2.o)
    0x08008f30   0x08008f30   0x000000b2   Code   RO         7278    x$fpl$retnan        g_wm.l(retnan.o)
    0x08008fe2   0x08008fe2   0x0000007e   Code   RO         7280    x$fpl$trapveneer    g_wm.l(trapv.o)
    0x08009060   0x08009060   0x00000000   Code   RO         7138    x$fpl$usenofp       g_wm.l(usenofp.o)
    0x08009060   0x08009060   0x00000028   Data   RO           23    .constdata          main.o
    0x08009088   0x08009088   0x00008000   Data   RO         6131    .constdata          dds_wavegen.o
    0x08011088   0x08011088   0x00000162   Data   RO         6381    .constdata          oled_1.o
    0x080111ea   0x080111ea   0x00000011   Data   RO         7025    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080111fb   0x080111fb   0x00000001   PAD
    0x080111fc   0x080111fc   0x00000008   Data   RO         7071    .constdata          c_w.l(_printf_wctomb.o)
    0x08011204   0x08011204   0x00000028   Data   RO         7100    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0801122c   0x0801122c   0x00000026   Data   RO         7164    .constdata          c_w.l(_printf_fp_hex.o)
    0x08011252   0x08011252   0x00000002   PAD
    0x08011254   0x08011254   0x00000094   Data   RO         7175    .constdata          c_w.l(bigflt0.o)
    0x080112e8   0x080112e8   0x00000020   Data   RO         7366    Region$$Table       anon$$obj.o
    0x08011308   0x08011308   0x0000001c   Data   RO         7201    locale$$data        c_w.l(lc_numeric_c.o)
    0x08011324   0x08011324   0x00000110   Data   RO         7288    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x080114b8, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08011434, Size: 0x00009fb8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08011434   0x00000014   Data   RW           24    .data               main.o
    0x20000014   0x08011448   0x00000014   Data   RW          341    .data               system_stm32f4xx.o
    0x20000028   0x0801145c   0x00000010   Data   RW         3340    .data               stm32f4xx_rcc.o
    0x20000038   0x0801146c   0x0000000c   Data   RW         5536    .data               adc_dma.o
    0x20000044   0x08011478   0x00000014   Data   RW         5680    .data               parallel_adc.o
    0x20000058   0x0801148c   0x00000019   Data   RW         5811    .data               systick.o
    0x20000071   0x080114a5   0x00000002   Data   RW         5951    .data               usart.o
    0x20000073   0x080114a7   0x00000002   Data   RW         6132    .data               dds_wavegen.o
    0x20000075   0x080114a9   0x00000003   PAD
    0x20000078   0x080114ac   0x00000008   Data   RW         6292    .data               key.o
    0x20000080   0x080114b4   0x00000001   Data   RW         6382    .data               oled_1.o
    0x20000081   0x080114b5   0x00000001   Data   RW         6696    .data               cd4052.o
    0x20000082   0x080114b6   0x00000002   PAD
    0x20000084        -       0x00000110   Zero   RW           22    .bss                main.o
    0x20000194        -       0x00004084   Zero   RW         5535    .bss                adc_dma.o
    0x20004218        -       0x00004058   Zero   RW         5679    .bss                parallel_adc.o
    0x20008270        -       0x00000010   Zero   RW         5810    .bss                systick.o
    0x20008280        -       0x00000e64   Zero   RW         5950    .bss                usart.o
    0x200090e4        -       0x00000464   Zero   RW         6130    .bss                dds_wavegen.o
    0x20009548        -       0x0000000c   Zero   RW         6291    .bss                key.o
    0x20009554        -       0x00000400   Zero   RW         6380    .bss                oled_1.o
    0x20009954        -       0x00000060   Zero   RW         7213    .bss                c_w.l(libspace.o)
    0x200099b4   0x080114b6   0x00000004   PAD
    0x200099b8        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x20009bb8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       706         42          0          0          0       5210   ad7606.o
      1590        168          0         12      16516      12547   adc_dma.o
        52          0          0          0          0        439   bsp.o
       168         16          0          1          0       1311   cd4052.o
       620         26          0          0          0       3959   dac8552.o
      1438        182      32768          2       1124      13965   dds_wavegen.o
       320         42          0          8         12       3726   key.o
      3022       1742         40         20        272     336646   main.o
       192         28          0          0          0     255388   misc.o
      1184         80        354          1       1024       7823   oled_1.o
       684         86          0         20      16472       9556   parallel_adc.o
        64         26        392          0       1536        836   startup_stm32f40_41xxx.o
      1972        338          0          0          0      22689   stm32f4xx_adc.o
       600         84          0          0          0       7194   stm32f4xx_dac.o
         0          0          0          0          0      15332   stm32f4xx_dfsdm.o
      3428        512          0          0          0      21048   stm32f4xx_dma.o
       528         98          0          0          0       4520   stm32f4xx_exti.o
      1468        362          0          0          0      10951   stm32f4xx_gpio.o
      1736        408          0          0          0      14463   stm32f4xx_i2c.o
        60          6          0          0          0       4297   stm32f4xx_it.o
       640        146          0         16          0       5176   stm32f4xx_rcc.o
       992        258          0          0          0      11854   stm32f4xx_spi.o
       220         32          0          0          0        736   stm32f4xx_syscfg.o
      1204        362          0          0          0      23633   stm32f4xx_tim.o
      2368        556          0          0          0      12698   stm32f4xx_usart.o
       340         32          0         20          0       1641   system_stm32f4xx.o
       508         88          0         25         16      32674   systick.o
      1186        122          0          2       3684      10832   usart.o

    ----------------------------------------------------------------------
     27310       <USER>      <GROUP>        132      40656     851144   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          4          0          0          0         84   _fptrap.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
       172        110          0          0          0         76   defsig_fpe_inner.o
        14          0          0          0          0         80   defsig_fpe_outer.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        26          0          0          0          0        116   dflt_clz.o
        12          0          0          0          0        116   dretinf.o
       492         36          0          0          0        148   except.o
       122          8          0          0          0        132   f2d.o
       242          4          0          0          0        144   fnaninf.o
        10          0          0          0          0        116   fpinit.o
       316          0          0          0          0        132   funder_clz.o
        32          4          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       178          0          0          0          0        116   retnan.o
       126          0          0          0          0        148   trapv.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      9258        <USER>        <GROUP>          0        100       6564   Library Totals
        22          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7624        386        551          0         96       4924   c_w.l
      1564         52          0          0          0       1516   g_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      9258        <USER>        <GROUP>          0        100       6564   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36568       6280      34140        132      40756     836652   Grand Totals
     36568       6280      34140        132      40756     836652   ELF Image Totals
     36568       6280      34140        132          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                70708 (  69.05kB)
    Total RW  Size (RW Data + ZI Data)             40888 (  39.93kB)
    Total ROM Size (Code + RO Data + RW Data)      70840 (  69.18kB)

==============================================================================

