#ifndef __SHOWOSC_H_
#define __SHOWOSC_H_

#include "stm32f4xx.h"

typedef struct{
	uint16_t x;
	uint16_t y;
	uint16_t wide;
	uint16_t height;
	uint8_t isAxesOn;
	uint16_t wave_amp_range;
	
	uint16_t color_par_num;
	uint16_t color_par_font;
	uint16_t color_par_frame;
	uint16_t color_par_bottom;
	uint16_t color_axes_line;
	uint16_t color_wave_line;
	uint16_t color_background_frame;
	uint16_t color_background_bottom;
} oscUI;

typedef struct{
	uint16_t x;
	uint16_t y;
	uint16_t font_size;
} oscPar;

void showOscUi(void);

void osc_Wave(void);
void osc_Axes(uint8_t isOn);
uint16_t osc_Background(uint16_t x, uint16_t y, uint16_t wide, uint16_t height, uint16_t color_frame, uint16_t color_bottom);
oscPar osc_Par(uint16_t x, uint16_t y, uint16_t wide, uint16_t height, uint8_t size_font, unsigned char str[]);

#endif



