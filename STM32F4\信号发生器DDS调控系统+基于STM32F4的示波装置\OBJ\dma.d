..\obj\dma.o: ..\ADC_DMA\dma.c
..\obj\dma.o: ..\ADC_DMA\adcdma.h
..\obj\dma.o: ..\USER\stm32f4xx.h
..\obj\dma.o: ..\CORE\core_cm4.h
..\obj\dma.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\dma.o: ..\CORE\core_cmInstr.h
..\obj\dma.o: ..\CORE\core_cmFunc.h
..\obj\dma.o: ..\CORE\core_cm4_simd.h
..\obj\dma.o: ..\USER\system_stm32f4xx.h
..\obj\dma.o: ..\USER\stm32f4xx_conf.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\dma.o: ..\USER\stm32f4xx.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\dma.o: ..\FWLIB\inc\misc.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\dma.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
