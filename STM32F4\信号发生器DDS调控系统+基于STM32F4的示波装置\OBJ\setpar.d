..\obj\setpar.o: ..\dds_ui\setPar.c
..\obj\setpar.o: ..\TFTLCD\lcd.h
..\obj\setpar.o: ..\SYSTEM\sys\sys.h
..\obj\setpar.o: ..\USER\stm32f4xx.h
..\obj\setpar.o: ..\CORE\core_cm4.h
..\obj\setpar.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\setpar.o: ..\CORE\core_cmInstr.h
..\obj\setpar.o: ..\CORE\core_cmFunc.h
..\obj\setpar.o: ..\CORE\core_cm4_simd.h
..\obj\setpar.o: ..\USER\system_stm32f4xx.h
..\obj\setpar.o: ..\USER\stm32f4xx_conf.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\setpar.o: ..\USER\stm32f4xx.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\setpar.o: ..\FWLIB\inc\misc.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\setpar.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\setpar.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\setpar.o: ..\Matrix_keys\key.h
..\obj\setpar.o: ..\SYSTEM\delay\delay.h
..\obj\setpar.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\setpar.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\setpar.o: ..\AD9833\ad9833.h
..\obj\setpar.o: ..\dds_ui\setPar.h
..\obj\setpar.o: ..\dds_ui\showPar.h
..\obj\setpar.o: ..\dds_ui\setParBmp.h
