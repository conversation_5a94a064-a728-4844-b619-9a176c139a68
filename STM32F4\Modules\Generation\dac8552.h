#ifndef __DAC8552_H
#define __DAC8552_H

#include "stm32f4xx.h"
#include "bsp.h"
#include "systick.h"

// DAC8552 通道定义
#define DAC8552_CHANNEL_A    0
#define DAC8552_CHANNEL_B    1

// DAC8552 控制字节定义 (基于商家FPGA代码验证)
#define DAC8552_CMD_WRITE_A  0x10  // 写入通道A并更新输出
#define DAC8552_CMD_WRITE_B  0x24  // 写入通道B并更新输出

// DAC8552 最大输出值 (16位)
#define DAC8552_MAX_VALUE    0xFFFF

// DAC8552 超时定义 (毫秒)
#define DAC8552_TIMEOUT_MS   10

// DAC8552 错误代码定义
#define DAC8552_OK           0
#define DAC8552_ERROR        1
#define DAC8552_TIMEOUT      2
#define DAC8552_PARAM_ERROR  3

// 函数声明
uint8_t DAC8552_Init(void);
uint8_t DAC8552_Write(uint8_t channel, uint16_t data);
uint8_t DAC8552_SetVoltage(uint8_t channel, float voltage);
uint8_t DAC8552_Test(void);

#endif
