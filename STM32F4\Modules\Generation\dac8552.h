#ifndef __DAC8552_H
#define __DAC8552_H

#include "stm32f4xx.h"
#include "bsp.h"

// DAC8552 通道定义
#define DAC8552_CHANNEL_A    0
#define DAC8552_CHANNEL_B    1

// DAC8552 控制字节定义
#define DAC8552_CMD_WRITE_A  0x10  // 写入通道A并更新
#define DAC8552_CMD_WRITE_B  0x24  // 写入通道B并更新

// DAC8552 最大输出值 (16位)
#define DAC8552_MAX_VALUE    0xFFFF

// 函数声明
void DAC8552_Init(void);
void DAC8552_Write(uint8_t channel, uint16_t data);
void DAC8552_SetVoltage(uint8_t channel, float voltage);

#endif
