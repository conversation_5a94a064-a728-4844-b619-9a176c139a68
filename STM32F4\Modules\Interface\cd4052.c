#include "cd4052.h"

// 静态变量保存当前增益级别
static uint8_t current_gain_level = CD4052_GAIN_LEVEL_0;

/**
 * @brief  CD4052初始化函数
 * @param  None
 * @retval CD4052_OK: 成功
 * @note   配置PE2和PE3为通用推挽输出
 */
uint8_t CD4052_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 1. 使能GPIOE时钟 (在BSP_Init中已使能)
    
    // 2. 配置A (PE2) 和 B (PE3) 引脚为通用推挽输出
    GPIO_InitStructure.GPIO_Pin = CD4052_A_PIN | CD4052_B_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(CD4052_CTRL_PORT, &GPIO_InitStructure);
    
    // 3. 默认设置为增益级别0 (B=0, A=0)
    CD4052_SetGain(CD4052_GAIN_LEVEL_0);

    return CD4052_OK;
}

/**
 * @brief  设置CD4052增益级别
 * @param  level: 增益级别 (0, 1, 2, 3)
 * @retval CD4052_OK: 成功, CD4052_PARAM_ERROR: 参数错误
 * @note   控制A和B引脚的电平来选择4个通道中的一个
 */
uint8_t CD4052_SetGain(uint8_t level)
{
    switch (level)
    {
        case CD4052_GAIN_LEVEL_0:  // B=0, A=0
            GPIO_ResetBits(CD4052_CTRL_PORT, CD4052_B_PIN);
            GPIO_ResetBits(CD4052_CTRL_PORT, CD4052_A_PIN);
            break;
            
        case CD4052_GAIN_LEVEL_1:  // B=0, A=1
            GPIO_ResetBits(CD4052_CTRL_PORT, CD4052_B_PIN);
            GPIO_SetBits(CD4052_CTRL_PORT, CD4052_A_PIN);
            break;
            
        case CD4052_GAIN_LEVEL_2:  // B=1, A=0
            GPIO_SetBits(CD4052_CTRL_PORT, CD4052_B_PIN);
            GPIO_ResetBits(CD4052_CTRL_PORT, CD4052_A_PIN);
            break;
            
        case CD4052_GAIN_LEVEL_3:  // B=1, A=1
            GPIO_SetBits(CD4052_CTRL_PORT, CD4052_B_PIN);
            GPIO_SetBits(CD4052_CTRL_PORT, CD4052_A_PIN);
            break;
            
        default:
            // 参数错误
            return CD4052_PARAM_ERROR;
    }

    // 保存当前增益级别
    current_gain_level = level;

    return CD4052_OK;
}

/**
 * @brief  获取当前CD4052增益级别
 * @param  None
 * @retval 当前增益级别 (0, 1, 2, 3)
 * @note   返回当前设置的增益级别
 */
uint8_t CD4052_GetGain(void)
{
    return current_gain_level;
}
