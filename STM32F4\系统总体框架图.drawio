<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="draw.io" etag="xxx" version="22.1.16" type="device">
  <diagram name="系统总体框架图" id="system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 人机接口层 -->
        <mxCell id="interface-layer" value="人机接口层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="oled" value="OLED显示屏&#xa;128x64像素&#xa;实时状态显示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="70" y="110" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="keys" value="按键输入&#xa;KEY0: 频率扫描&#xa;KEY1: 单点测试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="210" y="110" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="uart" value="串口调试&#xa;115200bps&#xa;数据输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="350" y="110" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 主控制器层 -->
        <mxCell id="mcu-layer" value="主控制器层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="220" width="420" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="stm32" value="STM32F407VGT6&#xa;Cortex-M4 168MHz&#xa;512KB Flash + 192KB RAM&#xa;硬件FPU + DSP指令集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="280" width="220" height="80" as="geometry" />
        </mxCell>
        
        <!-- 信号生成层 -->
        <mxCell id="generation-layer" value="信号生成层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dac8552" value="DAC8552&#xa;双通道16位DAC&#xa;SPI2接口&#xa;1MSPS更新率" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="70" y="460" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="buffer-out" value="输出缓冲&#xa;THS4001运放&#xa;低阻抗驱动&#xa;增益=1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="210" y="460" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="dds" value="DDS算法&#xa;256点正弦表&#xa;相位累加器&#xa;频率范围100Hz-1MHz" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="350" y="460" width="140" height="70" as="geometry" />
        </mxCell>
        
        <!-- 被测电路 -->
        <mxCell id="dut-layer" value="被测电路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="400" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="input-port" value="输入端口&#xa;U_in&#xa;激励信号输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;" vertex="1" parent="1">
          <mxGeometry x="570" y="460" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="dut" value="被测网络&#xa;DUT&#xa;待分析电路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="540" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="output-port" value="输出端口&#xa;U_out&#xa;响应信号输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;" vertex="1" parent="1">
          <mxGeometry x="570" y="620" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 信号调理层 -->
        <mxCell id="conditioning-layer" value="信号调理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="400" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="buffer-in" value="输入缓冲&#xa;THS4001运放&#xa;阻抗匹配&#xa;增益=1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="820" y="460" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="gain-control" value="增益控制&#xa;CD4052开关&#xa;四档增益&#xa;1×/2×/4×/8×" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="820" y="550" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="filter" value="抗混叠滤波&#xa;500kHz截止&#xa;防止频谱混叠&#xa;6阶巴特沃斯" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="820" y="640" width="120" height="70" as="geometry" />
        </mxCell>
        
        <!-- 数据采集层 -->
        <mxCell id="acquisition-layer" value="数据采集层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="750" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ad7606" value="AD7606&#xa;8通道16位ADC&#xa;200kSPS同步采集&#xa;SPI1接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="570" y="810" width="140" height="70" as="geometry" />
        </mxCell>
        
        <!-- 信号处理层 -->
        <mxCell id="processing-layer" value="信号处理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="750" width="420" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="digital-filter" value="数字滤波&#xa;噪声抑制&#xa;信号平滑&#xa;滑动平均" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="70" y="810" width="100" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="rms-calc" value="RMS计算&#xa;有效值测量&#xa;幅度分析&#xa;功率计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="190" y="810" width="100" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="phase-detect" value="相位检测&#xa;过零点算法&#xa;相位差计算&#xa;精度±2°" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="310" y="810" width="100" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="freq-response" value="频率响应&#xa;传递函数&#xa;H(jω)计算&#xa;幅频+相频" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;" vertex="1" parent="1">
          <mxGeometry x="430" y="810" width="100" height="70" as="geometry" />
        </mxCell>
        
        <!-- 控制算法层 -->
        <mxCell id="control-layer" value="控制算法层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="750" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="agc" value="AGC算法&#xa;自动增益控制&#xa;动态范围扩展&#xa;防饱和保护" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="820" y="810" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="pid" value="PID控制&#xa;闭环反馈&#xa;幅度稳定&#xa;Kp/Ki/Kd可调" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="960" y="810" width="120" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="freq-sweep" value="频率扫描&#xa;自动测试&#xa;数据记录&#xa;对数分布" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="890" y="900" width="120" height="70" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="oled" target="stm32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="keys" target="stm32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="stm32" target="dac8552">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="dac8552" target="buffer-out">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="buffer-out" target="input-port">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="input-port" target="dut">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="dut" target="output-port">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn8" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="output-port" target="buffer-in">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="buffer-in" target="gain-control">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="gain-control" target="filter">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn11" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="filter" target="ad7606">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ad7606" target="digital-filter">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 控制连接线 -->
        <mxCell id="ctrl1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#ff6b35;strokeWidth=2;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="agc" target="gain-control">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ctrl2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#ff6b35;strokeWidth=2;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="pid" target="dac8552">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ctrl3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#ff6b35;strokeWidth=2;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="freq-sweep" target="dds">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 反馈连接线 -->
        <mxCell id="feedback1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="freq-response" target="uart">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="feedback2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#2196f3;strokeWidth=2;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="freq-response" target="oled">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
