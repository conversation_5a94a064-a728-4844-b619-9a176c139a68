#include "ad7606.h"

// 定义NULL
#ifndef NULL
#define NULL ((void*)0)
#endif

// 私有函数声明
static void AD7606_Delay_us(uint32_t us);
static uint8_t AD7606_SPI_ReadWord(uint16_t* data);

/**
 * @brief  AD7606初始化函数
 * @param  None
 * @retval AD7606_OK: 成功, AD7606_ERROR: 失败
 * @note   配置SPI1和相关GPIO引脚，基于商家验证的参数优化
 */
uint8_t AD7606_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    SPI_InitTypeDef SPI_InitStructure;
    
    // 1. 使能时钟 (在BSP_Init中已使能)
    
    // 2. 配置SPI1引脚 (SCK, MISO)
    GPIO_InitStructure.GPIO_Pin = AD7606_SCK_PIN | AD7606_MISO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_SPI_PORT, &GPIO_InitStructure);
    
    // 3. 配置引脚复用功能
    GPIO_PinAFConfig(AD7606_SPI_PORT, AD7606_SCK_PINSOURCE, AD7606_SPI_AF);
    GPIO_PinAFConfig(AD7606_SPI_PORT, AD7606_MISO_PINSOURCE, AD7606_SPI_AF);
    
    // 4. 配置CS引脚为通用推挽输出
    GPIO_InitStructure.GPIO_Pin = AD7606_CS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CS_PORT, &GPIO_InitStructure);
    
    // 5. 配置控制引脚 (RESET, CONVST为输出，BUSY为输入)
    GPIO_InitStructure.GPIO_Pin = AD7606_RESET_PIN | AD7606_CONVST_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CTRL_PORT, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = AD7606_BUSY_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CTRL_PORT, &GPIO_InitStructure);
    
    // 6. 设置初始状态
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);    // RESET置高 (正常工作)
    GPIO_SetBits(AD7606_CS_PORT, AD7606_CS_PIN);         // CS置高 (未选中)
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN); // CONVST置低 (准备转换)
    
    // 7. 配置SPI1 (基于AD7606 datasheet优化)
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex; // 全双工
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;                     // 主机模式
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_16b;                // 16位数据帧
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;                      // CPOL=1 (AD7606要求)
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_2Edge;                     // CPHA=1 (AD7606要求)
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;                        // 软件NSS
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_32; // 降低到约5.25MHz，提高稳定性
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;               // MSB先发送
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(AD7606_SPI_PERIPH, &SPI_InitStructure);

    // 8. 使能SPI1
    SPI_Cmd(AD7606_SPI_PERIPH, ENABLE);

    // 9. 复位AD7606
    if (AD7606_Reset() != AD7606_OK)
    {
        return AD7606_ERROR;
    }

    // 10. 测试AD7606通信
    if (AD7606_Test() != AD7606_OK)
    {
        return AD7606_ERROR;
    }

    return AD7606_OK;
}

/**
 * @brief  启动AD7606转换
 * @param  None
 * @retval AD7606_OK: 成功, AD7606_BUSY_ERROR: 设备忙碌
 * @note   产生CONVST上升沿启动所有通道的同步转换，基于datasheet时序优化
 */
uint8_t AD7606_StartConversion(void)
{
    // 检查设备是否忙碌
    if (AD7606_IsBusy())
    {
        return AD7606_BUSY_ERROR;
    }

    // 产生CONVST脉冲 (基于AD7606 datasheet时序要求)
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);
    AD7606_Delay_us(1);  // 确保脉冲宽度 (最小25ns)
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);
    AD7606_Delay_us(1);  // 脉冲高电平时间
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);

    return AD7606_OK;
}

/**
 * @brief  读取AD7606所有通道数据
 * @param  pDataBuffer: 数据缓冲区指针 (至少8个int16_t)
 * @retval AD7606_OK: 成功, 其他: 错误代码
 * @note   读取8个通道的16位有符号数据，基于商家超时机制优化
 */
uint8_t AD7606_ReadData(int16_t* pDataBuffer)
{
    uint8_t i;
    uint32_t timeout_start;
    uint16_t temp_data;
    uint8_t result;

    // 1. 参数检查
    if (pDataBuffer == NULL)
    {
        return AD7606_PARAM_ERROR;
    }

    // 2. 启动转换
    result = AD7606_StartConversion();
    if (result != AD7606_OK)
    {
        return result;
    }

    // 3. 等待转换完成 (BUSY变为低电平) - 简化超时机制
    uint32_t timeout_count = 0;
    while (GPIO_ReadInputDataBit(AD7606_CTRL_PORT, AD7606_BUSY_PIN) == SET)
    {
        timeout_count++;
        if (timeout_count > 100000)  // 简单计数超时
        {
            return AD7606_TIMEOUT;
        }
    }

    // 4. 拉低CS开始读取数据
    GPIO_ResetBits(AD7606_CS_PORT, AD7606_CS_PIN);
    AD7606_Delay_us(1);  // 确保CS建立时间

    // 5. 读取8个通道的数据
    for (i = 0; i < AD7606_CHANNEL_COUNT; i++)
    {
        result = AD7606_SPI_ReadWord(&temp_data);
        if (result != AD7606_OK)
        {
            GPIO_SetBits(AD7606_CS_PORT, AD7606_CS_PIN);
            return result;
        }

        // 转换为有符号数据
        pDataBuffer[i] = (int16_t)temp_data;
    }

    // 6. 拉高CS结束读取
    AD7606_Delay_us(1);  // 确保最后一位数据稳定
    GPIO_SetBits(AD7606_CS_PORT, AD7606_CS_PIN);

    return AD7606_OK;
}

/**
 * @brief  复位AD7606
 * @param  None
 * @retval AD7606_OK: 成功
 * @note   产生复位脉冲，基于datasheet时序要求
 */
uint8_t AD7606_Reset(void)
{
    // 产生复位脉冲 (基于AD7606 datasheet: 最小50ns)
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);
    AD7606_Delay_us(10);  // 复位脉冲宽度
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);
    AD7606_Delay_us(500); // 复位后稳定时间 (datasheet要求)

    return AD7606_OK;
}

/**
 * @brief  检查AD7606是否忙碌
 * @param  None
 * @retval 0: 空闲, 1: 忙碌
 */
uint8_t AD7606_IsBusy(void)
{
    return GPIO_ReadInputDataBit(AD7606_CTRL_PORT, AD7606_BUSY_PIN);
}

/**
 * @brief  SPI读取16位数据 (带超时保护)
 * @param  data: 读取数据的指针
 * @retval AD7606_OK: 成功, AD7606_TIMEOUT: 超时
 * @note   基于商家HAL库超时机制优化
 */
static uint8_t AD7606_SPI_ReadWord(uint16_t* data)
{
    uint32_t timeout_count;

    // 发送虚拟数据
    timeout_count = 0;
    while (SPI_I2S_GetFlagStatus(AD7606_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET)
    {
        timeout_count++;
        if (timeout_count > 10000)
        {
            return AD7606_TIMEOUT;
        }
    }
    SPI_I2S_SendData(AD7606_SPI_PERIPH, 0x0000);

    // 接收实际数据
    timeout_count = 0;
    while (SPI_I2S_GetFlagStatus(AD7606_SPI_PERIPH, SPI_I2S_FLAG_RXNE) == RESET)
    {
        timeout_count++;
        if (timeout_count > 10000)
        {
            return AD7606_TIMEOUT;
        }
    }
    *data = SPI_I2S_ReceiveData(AD7606_SPI_PERIPH);

    return AD7606_OK;
}

/**
 * @brief  微秒级延时函数
 * @param  us: 延时时间(微秒)
 * @retval None
 * @note   简单的循环延时
 */
static void AD7606_Delay_us(uint32_t us)
{
    uint32_t i;
    // 简单的循环延时，假设168MHz系统时钟
    for (i = 0; i < us * 42; i++)
    {
        __NOP();
    }
}

/**
 * @brief  AD7606通信测试
 * @param  None
 * @retval AD7606_OK: 成功, AD7606_ERROR: 失败
 * @note   通过读取测试数据验证SPI通信
 */
uint8_t AD7606_Test(void)
{
    int16_t test_data[AD7606_CHANNEL_COUNT];
    uint8_t result;

    // 尝试读取一次数据
    result = AD7606_ReadData(test_data);
    if (result != AD7606_OK)
    {
        return result;
    }

    // 简单的数据合理性检查
    // AD7606的数据应该在合理范围内
    for (uint8_t i = 0; i < AD7606_CHANNEL_COUNT; i++)
    {
        if (test_data[i] == 0xFFFF || test_data[i] == 0x0000)
        {
            // 如果所有通道都是相同的极值，可能有问题
            // 但这不是绝对的错误，只是警告
        }
    }

    return AD7606_OK;
}
