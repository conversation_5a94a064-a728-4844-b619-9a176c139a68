#include "ad7606.h"

// 延时函数声明 (微秒级)
static void delay_us(uint32_t us);

/**
 * @brief  AD7606初始化函数
 * @param  None
 * @retval None
 * @note   配置SPI1和相关GPIO引脚
 */
void AD7606_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    SPI_InitTypeDef SPI_InitStructure;
    
    // 1. 使能时钟 (在BSP_Init中已使能)
    
    // 2. 配置SPI1引脚 (SCK, MISO)
    GPIO_InitStructure.GPIO_Pin = AD7606_SCK_PIN | AD7606_MISO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_SPI_PORT, &GPIO_InitStructure);
    
    // 3. 配置引脚复用功能
    GPIO_PinAFConfig(AD7606_SPI_PORT, AD7606_SCK_PINSOURCE, AD7606_SPI_AF);
    GPIO_PinAFConfig(AD7606_SPI_PORT, AD7606_MISO_PINSOURCE, AD7606_SPI_AF);
    
    // 4. 配置CS引脚为通用推挽输出
    GPIO_InitStructure.GPIO_Pin = AD7606_CS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CS_PORT, &GPIO_InitStructure);
    
    // 5. 配置控制引脚 (RESET, CONVST为输出，BUSY为输入)
    GPIO_InitStructure.GPIO_Pin = AD7606_RESET_PIN | AD7606_CONVST_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CTRL_PORT, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = AD7606_BUSY_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD7606_CTRL_PORT, &GPIO_InitStructure);
    
    // 6. 设置初始状态
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);    // RESET置高 (正常工作)
    GPIO_SetBits(AD7606_CS_PORT, AD7606_CS_PIN);         // CS置高 (未选中)
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN); // CONVST置低 (准备转换)
    
    // 7. 配置SPI1
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex; // 全双工
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;                     // 主机模式
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_16b;                // 16位数据帧
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;                      // CPOL=1
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_2Edge;                     // CPHA=1
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;                        // 软件NSS
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_16; // 分频系数
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;               // MSB先发送
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(AD7606_SPI_PERIPH, &SPI_InitStructure);
    
    // 8. 使能SPI1
    SPI_Cmd(AD7606_SPI_PERIPH, ENABLE);
    
    // 9. 复位AD7606
    AD7606_Reset();
}

/**
 * @brief  启动AD7606转换
 * @param  None
 * @retval None
 * @note   产生CONVST上升沿启动所有通道的同步转换
 */
void AD7606_StartConversion(void)
{
    // 产生CONVST上升沿
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);
    delay_us(1);  // 短暂延时
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);
    delay_us(1);  // 短暂延时
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_CONVST_PIN);
}

/**
 * @brief  读取AD7606所有通道数据
 * @param  pDataBuffer: 数据缓冲区指针 (至少8个int16_t)
 * @retval 0: 成功, 1: 超时失败
 * @note   读取8个通道的16位有符号数据
 */
uint8_t AD7606_ReadData(int16_t* pDataBuffer)
{
    uint8_t i;
    uint32_t timeout = 0;
    uint16_t temp_data;
    
    // 1. 启动转换
    AD7606_StartConversion();
    
    // 2. 等待转换完成 (BUSY变为低电平)
    timeout = 0;
    while (GPIO_ReadInputDataBit(AD7606_CTRL_PORT, AD7606_BUSY_PIN) == SET)
    {
        delay_us(1);
        timeout++;
        if (timeout > AD7606_CONV_TIMEOUT)
        {
            return 1;  // 超时失败
        }
    }
    
    // 3. 拉低CS开始读取数据
    GPIO_ResetBits(AD7606_CS_PORT, AD7606_CS_PIN);
    
    // 4. 读取8个通道的数据
    for (i = 0; i < AD7606_CHANNEL_COUNT; i++)
    {
        // 发送虚拟数据并接收实际数据
        while (SPI_I2S_GetFlagStatus(AD7606_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET);
        SPI_I2S_SendData(AD7606_SPI_PERIPH, 0x0000);
        
        while (SPI_I2S_GetFlagStatus(AD7606_SPI_PERIPH, SPI_I2S_FLAG_RXNE) == RESET);
        temp_data = SPI_I2S_ReceiveData(AD7606_SPI_PERIPH);
        
        // 转换为有符号数据
        pDataBuffer[i] = (int16_t)temp_data;
    }
    
    // 5. 拉高CS结束读取
    GPIO_SetBits(AD7606_CS_PORT, AD7606_CS_PIN);
    
    return 0;  // 成功
}

/**
 * @brief  复位AD7606
 * @param  None
 * @retval None
 * @note   产生复位脉冲
 */
void AD7606_Reset(void)
{
    GPIO_ResetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);
    delay_us(10);  // 复位脉冲宽度
    GPIO_SetBits(AD7606_CTRL_PORT, AD7606_RESET_PIN);
    delay_us(100); // 复位后稳定时间
}

/**
 * @brief  检查AD7606是否忙碌
 * @param  None
 * @retval 0: 空闲, 1: 忙碌
 */
uint8_t AD7606_IsBusy(void)
{
    return GPIO_ReadInputDataBit(AD7606_CTRL_PORT, AD7606_BUSY_PIN);
}

/**
 * @brief  微秒级延时函数
 * @param  us: 延时时间(微秒)
 * @retval None
 * @note   基于系统时钟的简单延时
 */
static void delay_us(uint32_t us)
{
    uint32_t i;
    // 假设系统时钟168MHz，粗略计算
    for (i = 0; i < us * 42; i++)
    {
        __NOP();
    }
}
