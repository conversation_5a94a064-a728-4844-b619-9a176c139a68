#include "dac8552.h"

/**
 * @brief  DAC8552初始化函数
 * @param  None
 * @retval None
 * @note   配置SPI2和相关GPIO引脚
 */
void DAC8552_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    SPI_InitTypeDef SPI_InitStructure;
    
    // 1. 使能时钟 (在BSP_Init中已使能)
    
    // 2. 配置SPI2引脚 (SCK, MOSI)
    GPIO_InitStructure.GPIO_Pin = DAC8552_SCK_PIN | DAC8552_MOSI_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(DAC8552_SPI_PORT, &GPIO_InitStructure);
    
    // 3. 配置引脚复用功能
    GPIO_PinAFConfig(DAC8552_SPI_PORT, DAC8552_SCK_PINSOURCE, DAC8552_SPI_AF);
    GPIO_PinAFConfig(DAC8552_SPI_PORT, DAC8552_MOSI_PINSOURCE, DAC8552_SPI_AF);
    
    // 4. 配置SYNC引脚为通用推挽输出
    GPIO_InitStructure.GPIO_Pin = DAC8552_SYNC_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(DAC8552_SYNC_PORT, &GPIO_InitStructure);
    
    // 5. SYNC引脚默认置高
    GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
    
    // 6. 配置SPI2
    SPI_InitStructure.SPI_Direction = SPI_Direction_1Line_Tx;  // 单向发送
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;              // 主机模式
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;          // 8位数据帧
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;               // CPOL=1
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;              // CPHA=0
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;                 // 软件NSS
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_32; // 分频系数
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;        // MSB先发送
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(DAC8552_SPI_PERIPH, &SPI_InitStructure);
    
    // 7. 使能SPI2
    SPI_Cmd(DAC8552_SPI_PERIPH, ENABLE);
}

/**
 * @brief  向DAC8552写入数据
 * @param  channel: 通道选择 (DAC8552_CHANNEL_A 或 DAC8552_CHANNEL_B)
 * @param  data: 16位DAC数据
 * @retval None
 * @note   发送24位数据：8位控制字节 + 16位数据
 */
void DAC8552_Write(uint8_t channel, uint16_t data)
{
    uint8_t cmd_byte;
    
    // 1. 根据通道选择控制字节
    if (channel == DAC8552_CHANNEL_A)
    {
        cmd_byte = DAC8552_CMD_WRITE_A;
    }
    else
    {
        cmd_byte = DAC8552_CMD_WRITE_B;
    }
    
    // 2. 拉低SYNC开始传输
    GPIO_ResetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
    
    // 3. 发送控制字节
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(DAC8552_SPI_PERIPH, cmd_byte);
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_BSY) == SET);
    
    // 4. 发送数据高8位
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(DAC8552_SPI_PERIPH, (uint8_t)(data >> 8));
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_BSY) == SET);
    
    // 5. 发送数据低8位
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(DAC8552_SPI_PERIPH, (uint8_t)(data & 0xFF));
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_BSY) == SET);
    
    // 6. 拉高SYNC结束传输
    GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
}

/**
 * @brief  设置DAC输出电压
 * @param  channel: 通道选择
 * @param  voltage: 输出电压值 (0.0V ~ 5.0V)
 * @retval None
 * @note   假设参考电压为5.0V
 */
void DAC8552_SetVoltage(uint8_t channel, float voltage)
{
    uint16_t dac_value;
    
    // 限制电压范围
    if (voltage < 0.0f) voltage = 0.0f;
    if (voltage > 5.0f) voltage = 5.0f;
    
    // 计算DAC数值 (假设Vref = 5.0V)
    dac_value = (uint16_t)(voltage * DAC8552_MAX_VALUE / 5.0f);
    
    // 写入DAC
    DAC8552_Write(channel, dac_value);
}
