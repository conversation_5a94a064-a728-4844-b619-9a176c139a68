#include "dac8552.h"

// 私有函数声明
static uint8_t DAC8552_SPI_SendByte(uint8_t data);
static void DAC8552_Delay_us(uint32_t us);

/**
 * @brief  DAC8552初始化函数
 * @param  None
 * @retval DAC8552_OK: 成功, DAC8552_ERROR: 失败
 * @note   配置SPI2和相关GPIO引脚，基于商家验证的参数优化
 */
uint8_t DAC8552_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    SPI_InitTypeDef SPI_InitStructure;
    
    // 1. 使能时钟 (在BSP_Init中已使能)
    
    // 2. 配置SPI2引脚 (SCK, MOSI)
    GPIO_InitStructure.GPIO_Pin = DAC8552_SCK_PIN | DAC8552_MOSI_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(DAC8552_SPI_PORT, &GPIO_InitStructure);
    
    // 3. 配置引脚复用功能
    GPIO_PinAFConfig(DAC8552_SPI_PORT, DAC8552_SCK_PINSOURCE, DAC8552_SPI_AF);
    GPIO_PinAFConfig(DAC8552_SPI_PORT, DAC8552_MOSI_PINSOURCE, DAC8552_SPI_AF);
    
    // 4. 配置SYNC引脚为通用推挽输出
    GPIO_InitStructure.GPIO_Pin = DAC8552_SYNC_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(DAC8552_SYNC_PORT, &GPIO_InitStructure);
    
    // 5. SYNC引脚默认置高
    GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
    
    // 6. 配置SPI2 (基于商家FPGA代码优化，降低时钟频率提高稳定性)
    SPI_InitStructure.SPI_Direction = SPI_Direction_1Line_Tx;  // 单向发送
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;              // 主机模式
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;          // 8位数据帧
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;               // CPOL=1 (DAC8552要求)
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;              // CPHA=0 (DAC8552要求)
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;                 // 软件NSS
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_64; // 降低到约2.6MHz，提高稳定性
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;        // MSB先发送
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(DAC8552_SPI_PERIPH, &SPI_InitStructure);

    // 7. 使能SPI2
    SPI_Cmd(DAC8552_SPI_PERIPH, ENABLE);

    // 8. 初始化延时，确保SPI稳定
    DAC8552_Delay_us(100);

    // 9. 测试SPI通信
    if (DAC8552_Test() != DAC8552_OK)
    {
        return DAC8552_ERROR;
    }

    return DAC8552_OK;
}

/**
 * @brief  向DAC8552写入数据
 * @param  channel: 通道选择 (DAC8552_CHANNEL_A 或 DAC8552_CHANNEL_B)
 * @param  data: 16位DAC数据
 * @retval DAC8552_OK: 成功, DAC8552_ERROR: 失败, DAC8552_PARAM_ERROR: 参数错误
 * @note   发送24位数据：8位控制字节 + 16位数据，基于商家时序优化
 */
uint8_t DAC8552_Write(uint8_t channel, uint16_t data)
{
    uint8_t cmd_byte;
    uint8_t result;

    // 1. 参数检查
    if (channel > DAC8552_CHANNEL_B)
    {
        return DAC8552_PARAM_ERROR;
    }

    // 2. 根据通道选择控制字节
    if (channel == DAC8552_CHANNEL_A)
    {
        cmd_byte = DAC8552_CMD_WRITE_A;
    }
    else
    {
        cmd_byte = DAC8552_CMD_WRITE_B;
    }

    // 3. 拉低SYNC开始传输 (参考商家FPGA时序)
    GPIO_ResetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
    DAC8552_Delay_us(1);  // 确保SYNC建立时间

    // 4. 发送控制字节
    result = DAC8552_SPI_SendByte(cmd_byte);
    if (result != DAC8552_OK)
    {
        GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
        return result;
    }

    // 5. 发送数据高8位
    result = DAC8552_SPI_SendByte((uint8_t)(data >> 8));
    if (result != DAC8552_OK)
    {
        GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
        return result;
    }

    // 6. 发送数据低8位
    result = DAC8552_SPI_SendByte((uint8_t)(data & 0xFF));
    if (result != DAC8552_OK)
    {
        GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
        return result;
    }

    // 7. 拉高SYNC结束传输 (参考商家100ns延时)
    DAC8552_Delay_us(1);  // 确保最后一位数据稳定
    GPIO_SetBits(DAC8552_SYNC_PORT, DAC8552_SYNC_PIN);
    DAC8552_Delay_us(1);  // 确保SYNC保持时间

    return DAC8552_OK;
}

/**
 * @brief  设置DAC输出电压
 * @param  channel: 通道选择
 * @param  voltage: 输出电压值 (0.0V ~ 5.0V)
 * @retval DAC8552_OK: 成功, 其他: 错误代码
 * @note   假设参考电压为5.0V
 */
uint8_t DAC8552_SetVoltage(uint8_t channel, float voltage)
{
    uint16_t dac_value;

    // 参数检查
    if (channel > DAC8552_CHANNEL_B)
    {
        return DAC8552_PARAM_ERROR;
    }

    // 限制电压范围
    if (voltage < 0.0f) voltage = 0.0f;
    if (voltage > 5.0f) voltage = 5.0f;

    // 计算DAC数值 (假设Vref = 5.0V)
    dac_value = (uint16_t)(voltage * DAC8552_MAX_VALUE / 5.0f);

    // 写入DAC
    return DAC8552_Write(channel, dac_value);
}

/**
 * @brief  SPI发送单字节数据 (带超时保护)
 * @param  data: 要发送的数据
 * @retval DAC8552_OK: 成功, DAC8552_TIMEOUT: 超时
 * @note   基于商家HAL库超时机制优化
 */
static uint8_t DAC8552_SPI_SendByte(uint8_t data)
{
    uint32_t timeout_count = 0;

    // 等待发送缓冲区空
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_TXE) == RESET)
    {
        timeout_count++;
        if (timeout_count > 10000)
        {
            return DAC8552_TIMEOUT;
        }
    }

    // 发送数据
    SPI_I2S_SendData(DAC8552_SPI_PERIPH, data);

    // 等待发送完成
    timeout_count = 0;
    while (SPI_I2S_GetFlagStatus(DAC8552_SPI_PERIPH, SPI_I2S_FLAG_BSY) == SET)
    {
        timeout_count++;
        if (timeout_count > 10000)
        {
            return DAC8552_TIMEOUT;
        }
    }

    return DAC8552_OK;
}

/**
 * @brief  微秒级延时函数
 * @param  us: 延时时间(微秒)
 * @retval None
 * @note   简单的循环延时
 */
static void DAC8552_Delay_us(uint32_t us)
{
    uint32_t i;
    // 简单的循环延时，假设168MHz系统时钟
    for (i = 0; i < us * 42; i++)
    {
        __NOP();
    }
}

/**
 * @brief  DAC8552通信测试
 * @param  None
 * @retval DAC8552_OK: 成功, DAC8552_ERROR: 失败
 * @note   通过写入已知值测试SPI通信
 */
uint8_t DAC8552_Test(void)
{
    uint8_t result;

    // 测试通道A - 写入中间值
    result = DAC8552_Write(DAC8552_CHANNEL_A, 0x8000);
    if (result != DAC8552_OK)
    {
        return result;
    }

    // 短暂延时
    DAC8552_Delay_us(100);

    // 测试通道B - 写入中间值
    result = DAC8552_Write(DAC8552_CHANNEL_B, 0x8000);
    if (result != DAC8552_OK)
    {
        return result;
    }

    return DAC8552_OK;
}
