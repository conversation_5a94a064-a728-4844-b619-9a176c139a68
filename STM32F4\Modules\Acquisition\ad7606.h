#ifndef __AD7606_H
#define __AD7606_H

#include "stm32f4xx.h"
#include "bsp.h"

// AD7606 通道数量
#define AD7606_CHANNEL_COUNT    8

// AD7606 转换超时时间 (微秒)
#define AD7606_CONV_TIMEOUT     10

// AD7606 数据范围 (16位有符号)
#define AD7606_MAX_VALUE        32767
#define AD7606_MIN_VALUE        -32768

// 函数声明
void AD7606_Init(void);
void AD7606_StartConversion(void);
uint8_t AD7606_ReadData(int16_t* pDataBuffer);
void AD7606_Reset(void);
uint8_t AD7606_IsBusy(void);

#endif
