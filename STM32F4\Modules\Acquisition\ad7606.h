#ifndef __AD7606_H
#define __AD7606_H

#include "stm32f4xx.h"
#include "bsp.h"

// AD7606 通道数量
#define AD7606_CHANNEL_COUNT    8

// AD7606 转换超时时间 (毫秒) - 基于商家代码优化
#define AD7606_CONV_TIMEOUT_MS  50
#define AD7606_SPI_TIMEOUT_MS   10

// AD7606 数据范围 (16位有符号)
#define AD7606_MAX_VALUE        32767
#define AD7606_MIN_VALUE        -32768

// AD7606 错误代码定义
#define AD7606_OK               0
#define AD7606_ERROR            1
#define AD7606_TIMEOUT          2
#define AD7606_PARAM_ERROR      3
#define AD7606_BUSY_ERROR       4

// 函数声明
uint8_t AD7606_Init(void);
uint8_t AD7606_StartConversion(void);
uint8_t AD7606_ReadData(int16_t* pDataBuffer);
uint8_t AD7606_Reset(void);
uint8_t AD7606_IsBusy(void);
uint8_t AD7606_Test(void);

#endif
