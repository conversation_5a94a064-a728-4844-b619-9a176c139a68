# STM32F4电赛G题项目 - Keil配置更新指南

## 📋 概述

本文档提供STM32F407电赛G题项目在Keil MDK环境下的完整配置更新指南，确保新增的DAC8552、AD7606、CD4052驱动能够正确编译和运行。

## 🎯 硬件平台

- **开发板**: 嘉立创"天空星"STM32F407VGT6
- **开发环境**: Keil MDK 5.x
- **软件库**: STM32F4标准外设库(SPL)
- **系统时钟**: 168MHz

## 📁 更新后的项目文件结构

```
STM32F4_Contest_Project/
├── User/
│   ├── main.c                    # 集成G题驱动的主程序
│   ├── main.h
│   ├── bsp.c/h                   # 板级支持包
│   ├── stm32f4xx_conf.h
│   └── optimized_example.c       # 完整测试示例
├── Modules/
│   ├── Core/
│   │   ├── systick.c/h
│   │   └── usart.c/h
│   ├── Acquisition/
│   │   ├── adc_dma.c/h
│   │   ├── parallel_adc.c/h
│   │   └── ad7606.c/h            # 新增：AD7606驱动(优化版)
│   ├── Generation/
│   │   ├── dds_wavegen.c/h
│   │   └── dac8552.c/h           # 新增：DAC8552驱动(优化版)
│   ├── Processing/
│   │   └── fft.c/h
│   └── Interface/
│       ├── oled.c/h
│       ├── key.c/h
│       └── cd4052.c/h            # 新增：CD4052驱动(优化版)
├── Libraries/
│   ├── STM32F4xx_StdPeriph_Driver/
│   ├── CMSIS/
│   └── startup_stm32f40_41xxx.s
└── Project/
    └── STM32F407.uvprojx
```

## ⚙️ Keil项目配置更新步骤

### 1. 添加新的源文件到项目

在Keil项目中添加以下新文件：

#### Modules组 - 新增文件
- `Modules/Generation/dac8552.c`
- `Modules/Acquisition/ad7606.c`
- `Modules/Interface/cd4052.c`

#### User组 - 更新文件
- `User/main.c` (已集成G题驱动)
- `User/bsp.c/h` (板级支持包)

### 2. 更新Include路径配置

在C/C++选项卡的Include Paths中确认包含：

```
..\User
..\Modules\Core
..\Modules\Acquisition
..\Modules\Generation
..\Modules\Processing
..\Modules\Interface
..\Libraries\STM32F4xx_StdPeriph_Driver\inc
..\Libraries\CMSIS\Device\ST\STM32F4xx\Include
..\Libraries\CMSIS\Include
..\Libraries\CMSIS\DSP\Include
```

### 3. 确认宏定义配置

在C/C++选项卡的Define中确认：

```
STM32F40_41xxx,USE_STDPERIPH_DRIVER,ARM_MATH_CM4,__FPU_PRESENT=1
```

### 4. 验证Target配置

在Target选项卡中确认：
- **Device**: STM32F407VGTx
- **Floating Point Hardware**: Use FPU (重要！)
- **Crystal (MHz)**: 25.0

## 🔧 新增驱动模块说明

### 1. DAC8552双通道DAC驱动
- **文件**: `dac8552.c/h`
- **接口**: SPI2 (PB13/PB15/PB12)
- **特性**: 超时保护、错误处理、商家验证参数
- **函数**: `DAC8552_Init()`, `DAC8552_SetVoltage()`, `DAC8552_Write()`

### 2. AD7606同步ADC驱动
- **文件**: `ad7606.c/h`
- **接口**: SPI1 (PA5/PA6/PA15) + 控制引脚(PC6/PC7/PC8)
- **特性**: 8通道同步采集、毫秒级超时、状态检查
- **函数**: `AD7606_Init()`, `AD7606_ReadData()`, `AD7606_StartConversion()`

### 3. CD4052增益控制驱动
- **文件**: `cd4052.c/h`
- **接口**: PE2/PE3 (数字控制)
- **特性**: 4级增益控制、状态记录、参数验证
- **函数**: `CD4052_Init()`, `CD4052_SetGain()`, `CD4052_GetGain()`

## 🚀 编译验证步骤

### 1. 清理项目
- 选择 `Project` -> `Clean Targets`
- 删除 `Objects` 和 `Listings` 文件夹内容

### 2. 重新编译
- 按 `F7` 或选择 `Project` -> `Build Target`
- 检查编译输出，确保无错误

### 3. 预期编译结果
```
*** Using Compiler 'V5.06 update 5 (build 528)'
Build target 'Target 1'
compiling main.c...
compiling bsp.c...
compiling dac8552.c...
compiling ad7606.c...
compiling cd4052.c...
...
Build succeeded.
```

## 🔍 常见编译问题及解决方案

### 问题1: "找不到头文件"
**解决方案**: 检查Include Paths配置，确保包含所有Modules子目录

### 问题2: "函数未定义"
**解决方案**: 确认所有.c文件已添加到项目中

### 问题3: "SysTick相关错误"
**解决方案**: 确保包含了`systick.h`并且systick.c已编译

### 问题4: "BSP相关错误"
**解决方案**: 确保bsp.c/h文件在User组中

## 📊 功能验证

### 1. 基本功能测试
编译成功后，程序将自动执行：
- BSP初始化
- G题专用模块初始化
- 基本功能测试

### 2. 串口输出验证
连接串口调试工具，波特率115200，应看到：
```
=== STM32F4竞赛级模块库演示 ===
=== 电赛G题专用模块初始化 ===
DAC8552初始化成功
CD4052初始化成功
AD7606初始化成功
=== G题专用模块测试 ===
```

### 3. 按键功能验证
- **KEY0**: 切换增益级别 (CD4052)
- **KEY1**: 调整DAC输出电压 (DAC8552)

## 🎯 电赛实战建议

### 1. 调试优化
- 使用串口输出监控系统状态
- 利用OLED显示关键参数
- 通过按键快速切换测试模式

### 2. 性能监控
- 监控ADC采集频率和精度
- 验证DAC输出稳定性
- 检查增益切换响应时间

### 3. 故障排除
- 检查硬件连接
- 验证SPI通信时序
- 监控错误代码返回

## 🚨 重要提醒

1. **FPU配置**: 必须启用FPU，否则浮点运算会出错
2. **时钟配置**: 确保系统时钟为168MHz
3. **引脚配置**: 严格按照bsp.h中的定义连接硬件
4. **超时设置**: 根据实际需求调整超时参数
5. **错误处理**: 重视函数返回的错误代码

---

**版权所有 © 2024 电赛仪器仪表专家系统**

**最后更新**: 2024年 - 商家代码优化集成版本
